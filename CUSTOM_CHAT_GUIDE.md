# 自定义聊天组件使用指南

## 概述

已成功集成自定义聊天组件，完全替换了腾讯云IM原生组件，使用真实的IM数据和您的自定义消息格式。

## 功能特性

### 🎯 真实IM数据集成
- **真实会话列表**：从腾讯云IM获取真实的会话数据
- **真实消息历史**：显示真实的聊天记录
- **实时消息同步**：新消息实时推送和显示
- **消息已读状态**：支持消息已读标记

### 🔄 消息格式转换
- **双向转换**：腾讯云IM格式 ↔ 自定义消息格式
- **完整支持您的消息格式**：fromUser、toUser、msgType、time、data
- **多种消息类型**：文本、图片、视频、语音、系统通知
- **消息格式验证**：确保消息格式的完整性和正确性

### 💬 聊天功能
- **真实会话列表**：显示所有真实聊天会话，支持搜索
- **消息显示**：根据消息类型智能显示不同内容
- **消息发送**：通过腾讯云IM SDK发送真实消息
- **实时更新**：消息发送和接收实时更新

### 🎨 界面特性
- **响应式设计**：适配不同屏幕尺寸
- **美观界面**：现代化的聊天界面设计
- **用户体验**：流畅的交互和动画效果
- **聊天对象信息**：右侧显示当前聊天对象的详细信息

## 消息格式说明

### 消息主体结构
```typescript
interface CustomMessage {
  fromUser?: CustomUser;     // 发送者
  toUser?: CustomUser;       // 接收者
  msgType?: number;          // 1000-系统消息 2000-私聊消息
  time?: string;             // 毫秒级时间戳
  data?: CustomMessageData;  // 消息内容
}
```

### 用户信息结构
```typescript
interface CustomUser {
  userId: string;    // 用户ID
  nickname: string;  // 昵称
  avatar: string;    // 头像地址
}
```

### 消息内容结构
```typescript
interface CustomMessageData {
  type: number;           // 1001-系统通知 2001-文本 2002-图片 2003-视频 2004-语音
  text?: string;          // 文本内容
  image?: string;         // 图片地址
  video?: string;         // 视频地址
  voice?: string;         // 语音地址
  voiceSeconds?: number;  // 语音时长（秒）
  postId?: string;        // 帖子ID
}
```

## 使用方法

### 1. 启动应用
```bash
yarn dev
```

### 2. 访问应用
打开浏览器访问：`http://localhost:5174`

### 3. 开始聊天
- 在左侧会话列表中选择一个真实会话
- 右侧会显示聊天对象的详细信息
- 在聊天界面中发送消息
- 支持发送文本、图片、视频、语音等多种类型消息
- 消息会通过腾讯云IM实时发送和接收

## 技术实现

### 核心组件
1. **CustomConversationList** - 自定义会话列表组件，显示真实会话数据
2. **CustomChat** - 自定义聊天容器组件，集成真实消息列表
3. **CustomMessage** - 自定义消息显示组件，支持多种消息类型
4. **CustomMessageInput** - 自定义消息输入组件，通过IM SDK发送消息

### IM数据集成
- **useChat.ts** - 聊天钩子，集成腾讯云IM SDK
- **useConversation** - 获取真实会话列表
- **useMessageList** - 获取真实消息历史
- **消息格式转换** - IM格式与自定义格式的双向转换

### 真实IM功能
- 使用腾讯云IM SDK发送和接收消息
- 实时消息同步和推送
- 消息已读状态管理
- 会话列表实时更新

## 扩展功能

### 已实现
✅ 真实会话列表显示
✅ 真实消息历史加载
✅ 实时消息发送和接收
✅ 文本消息发送和显示
✅ 图片消息支持（带预览）
✅ 视频消息支持
✅ 语音消息支持
✅ 系统通知消息
✅ 消息时间显示
✅ 未读消息计数
✅ 消息已读状态
✅ 聊天对象信息显示
✅ 消息格式双向转换

### 待扩展
🔄 文件上传到服务器
🔄 语音录制功能
🔄 表情包支持
🔄 消息撤回功能
🔄 消息转发功能
🔄 群聊支持
🔄 消息搜索功能

## 配置说明

### 环境变量
确保以下环境变量已配置：
- `VITE_HOST`: API服务器地址
- `VITE_DEBUG`: 调试模式

### IM配置
消息发送使用腾讯云IM SDK：
```typescript
// IM SDK配置
const chat = TencentCloudChat.create({ SDKAppID: 1600063567 });
// 发送消息
await chat.sendMessage(timMessage);
```

## 故障排除

### 常见问题

**Q: 会话列表为空？**
A: 确保用户已登录IM并且有聊天记录，检查userSig是否有效。

**Q: 消息发送失败？**
A: 检查网络连接和IM配置，确保SDKAppID和userSig正确。

**Q: 图片/视频上传不工作？**  
A: 当前使用本地URL作为示例，需要集成实际的文件上传服务。

**Q: 语音功能不可用？**  
A: 语音录制功能需要进一步开发，当前为模拟实现。

### 开发调试
1. 打开浏览器开发者工具
2. 查看Console面板的日志信息
3. 检查Network面板的API请求

## 下一步计划

1. **完善文件上传**：集成真实的文件上传服务
2. **语音功能**：实现语音录制和播放
3. **群聊支持**：扩展支持群聊功能
4. **消息同步**：实现实时消息同步
5. **离线消息**：支持离线消息存储和同步

## 技术支持

如有问题，请查看：
1. 控制台错误信息
2. 网络请求状态
3. 消息格式是否正确
4. API响应内容
