import {UserPostListResponse} from "@/apis/index";

export interface SourceListItem {
  type: 1 | 2; // 1-图片 2-视频
  url: string;
}

export enum MessageType {
  System = 1000,
  Person = 2000,
  Text = 2001,
  Image = 2002,
}

// 自定义消息格式定义
export interface CustomUser {
  userId: string;
  nickname: string;
  avatar: string;
}

export interface CustomMessageData {
  type: number; // 1001-系统通知 2001-文本消息 2002-图片消息 2003-视频消息 2004-语音消息
  text?: string;
  image?: string;
  video?: string;
  voice?: string;
  voiceSeconds?: number; // 语音时长，秒
  postId?: string;
}

export interface CustomMessage {
  fromUser?: CustomUser;
  toUser?: CustomUser;
  msgType?: number; // 1000-系统消息 2000-私聊消息
  time?: string; // 毫秒级时间戳
  data?: CustomMessageData;
}

export interface IPostItem extends UserPostListResponse {
  sourceList?: SourceListItem[]
}
