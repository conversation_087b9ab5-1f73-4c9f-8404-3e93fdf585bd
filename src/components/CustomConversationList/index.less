@import '@/styles/var';

.custom-conversation-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: @white;
  border-right: 1px solid @line-1;
  
  .conversation-header {
    padding: 16px;
    border-bottom: 1px solid @line-1;
    flex-shrink: 0;
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 18px;
      font-weight: 600;
      color: @text-color-5;
    }
    
    .conversation-search {
      .ant-input {
        border-radius: 8px;
        border: 1px solid @line-1;
        
        &:focus {
          border-color: @primary-color;
          box-shadow: 0 0 0 2px rgba(48, 190, 143, 0.1);
        }
      }
    }
  }
  
  .conversation-list {
    flex: 1;
    overflow-y: auto;
    
    .no-conversations {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
    
    .conversation-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid @line-1;
      transition: background-color 0.2s;
      
      &:hover {
        background-color: @line-1;
      }
      
      &.selected {
        background-color: @primary-color-light;
        border-right: 3px solid @primary-color;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .conversation-avatar {
        margin-right: 12px;
        flex-shrink: 0;
        
        .ant-badge {
          .ant-badge-count {
            background-color: @error;
            border: 1px solid @white;
          }
        }
      }
      
      .conversation-content {
        flex: 1;
        min-width: 0; // 防止文本溢出
        
        .conversation-header-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          
          .conversation-name {
            font-size: 14px;
            font-weight: 500;
            color: @text-color-5;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            margin-right: 8px;
          }
          
          .conversation-time {
            font-size: 12px;
            color: @text-color-3;
            white-space: nowrap;
          }
        }
        
        .conversation-last-message {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .last-message-text {
            font-size: 13px;
            color: @text-color-3;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            margin-right: 8px;
          }
          
          .unread-badge {
            .ant-badge-count {
              background-color: @error;
              font-size: 10px;
              min-width: 16px;
              height: 16px;
              line-height: 16px;
              padding: 0 4px;
            }
          }
        }
      }
    }
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: @line-2;
      border-radius: 3px;
      
      &:hover {
        background-color: @line-3;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-conversation-list {
    .conversation-header {
      padding: 12px;
      
      h3 {
        font-size: 16px;
        margin-bottom: 8px;
      }
    }
    
    .conversation-list {
      .conversation-item {
        padding: 10px 12px;
        
        .conversation-avatar {
          margin-right: 10px;
          
          .ant-avatar {
            width: 40px !important;
            height: 40px !important;
          }
        }
        
        .conversation-content {
          .conversation-header-info {
            .conversation-name {
              font-size: 13px;
            }
            
            .conversation-time {
              font-size: 11px;
            }
          }
          
          .conversation-last-message {
            .last-message-text {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
