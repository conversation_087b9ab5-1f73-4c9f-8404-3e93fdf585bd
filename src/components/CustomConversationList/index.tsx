import React, { useState, useEffect } from 'react';
import { Input, Badge, Avatar, Empty } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { CustomUser, CustomMessage } from '@/apis/exact';
import './index.less';

const { Search } = Input;

interface Conversation {
  id: string;
  user: CustomUser;
  lastMessage?: CustomMessage;
  unreadCount: number;
  lastMessageTime: string;
}

interface CustomConversationListProps {
  conversations: Conversation[];
  currentUser: CustomUser;
  selectedConversationId?: string;
  onConversationSelect: (conversation: Conversation) => void;
  onSearch?: (keyword: string) => void;
  loading?: boolean;
}

const CustomConversationList: React.FC<CustomConversationListProps> = ({
  conversations,
  currentUser,
  selectedConversationId,
  onConversationSelect,
  onSearch,
  loading = false
}) => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filteredConversations, setFilteredConversations] = useState<Conversation[]>(conversations);

  // 过滤会话列表
  useEffect(() => {
    if (!searchKeyword.trim()) {
      setFilteredConversations(conversations);
    } else {
      const filtered = conversations.filter(conv =>
        conv.user.nickname.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        conv.user.userId.toLowerCase().includes(searchKeyword.toLowerCase())
      );
      setFilteredConversations(filtered);
    }
  }, [conversations, searchKeyword]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    onSearch?.(value);
  };

  // 格式化最后消息时间
  const formatLastMessageTime = (timestamp: string) => {
    const date = new Date(parseInt(timestamp));
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else if (diff < 604800000) { // 7天内
      return `${Math.floor(diff / 86400000)}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // 获取最后消息的显示文本
  const getLastMessageText = (message?: CustomMessage) => {
    if (!message?.data) return '暂无消息';
    
    switch (message.data.type) {
      case 1001:
        return '[系统通知]';
      case 2001:
        return message.data.text || '';
      case 2002:
        return '[图片]';
      case 2003:
        return '[视频]';
      case 2004:
        return '[语音]';
      default:
        return '[消息]';
    }
  };

  return (
    <div className="custom-conversation-list">
      <div className="conversation-header">
        <h3>消息</h3>
        <Search
          placeholder="搜索联系人"
          allowClear
          prefix={<SearchOutlined />}
          onSearch={handleSearch}
          onChange={(e) => setSearchKeyword(e.target.value)}
          className="conversation-search"
        />
      </div>

      <div className="conversation-list">
        {filteredConversations.length === 0 ? (
          <div className="no-conversations">
            <Empty 
              description={searchKeyword ? "未找到相关会话" : "暂无会话"}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              className={`conversation-item ${
                selectedConversationId === conversation.id ? 'selected' : ''
              }`}
              onClick={() => onConversationSelect(conversation)}
            >
              <div className="conversation-avatar">
                <Badge count={conversation.unreadCount} size="small">
                  <Avatar 
                    src={conversation.user.avatar}
                    size={48}
                  >
                    {conversation.user.nickname?.charAt(0) || 'U'}
                  </Avatar>
                </Badge>
              </div>

              <div className="conversation-content">
                <div className="conversation-header-info">
                  <span className="conversation-name">
                    {conversation.user.nickname}
                  </span>
                  <span className="conversation-time">
                    {formatLastMessageTime(conversation.lastMessageTime)}
                  </span>
                </div>

                <div className="conversation-last-message">
                  <span className="last-message-text">
                    {getLastMessageText(conversation.lastMessage)}
                  </span>
                  {conversation.unreadCount > 0 && (
                    <Badge 
                      count={conversation.unreadCount} 
                      size="small"
                      className="unread-badge"
                    />
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default CustomConversationList;
export type { Conversation };
