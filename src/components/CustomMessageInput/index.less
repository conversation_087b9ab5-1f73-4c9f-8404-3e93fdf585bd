@import '@/styles/var';

.custom-message-input {
  border-top: 1px solid @line-1;
  background-color: @white;
  padding: 12px 16px;
  
  .input-toolbar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 8px;
    
    .toolbar-button {
      color: @text-color-3;
      border: none;
      padding: 4px 8px;
      
      &:hover {
        color: @primary-color;
        background-color: @line-1;
      }
      
      &:disabled {
        color: @text-color-4;
        background-color: transparent;
      }
    }
  }
  
  .input-area {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    
    .message-textarea {
      flex: 1;
      border-radius: 8px;
      border: 1px solid @line-1;
      resize: none;
      
      &:focus {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px rgba(48, 190, 143, 0.1);
      }
      
      &:disabled {
        background-color: @line-1;
        color: @text-color-4;
      }
      
      .ant-input {
        border: none;
        box-shadow: none;
        padding: 8px 12px;
        
        &:focus {
          box-shadow: none;
        }
      }
    }
    
    .send-button {
      background-color: @primary-color;
      border-color: @primary-color;
      border-radius: 8px;
      height: auto;
      padding: 8px 16px;
      
      &:hover {
        background-color: @success;
        border-color: @success;
      }
      
      &:disabled {
        background-color: @line-2;
        border-color: @line-2;
        color: @text-color-4;
      }
    }
  }
  
  .recording-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    padding: 8px;
    background-color: @error-light;
    border-radius: 8px;
    color: @error;
    
    .recording-animation {
      width: 8px;
      height: 8px;
      background-color: @error;
      border-radius: 50%;
      margin-right: 8px;
      animation: pulse 1s infinite;
    }
    
    span {
      font-size: 14px;
    }
  }
}

.more-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 100px;
  
  .action-button {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    color: @text-color-5;
    
    &:hover {
      background-color: @line-1;
      color: @primary-color;
    }
    
    &:disabled {
      color: @text-color-4;
      background-color: transparent;
    }
    
    .anticon {
      margin-right: 8px;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
