import React, { useState, useRef } from 'react';
import { Input, Button, Upload, message, Popover } from 'antd';
import { 
  SendOutlined, 
  PictureOutlined, 
  VideoCameraOutlined, 
  AudioOutlined,
  SmileOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { CustomMessage, CustomUser, CustomMessageData } from '@/apis/exact';
import { postMessageSend } from '@/apis';
import './index.less';

const { TextArea } = Input;

interface CustomMessageInputProps {
  currentUser: CustomUser;
  targetUser: CustomUser;
  onMessageSent?: (message: CustomMessage) => void;
  disabled?: boolean;
}

const CustomMessageInput: React.FC<CustomMessageInputProps> = ({
  currentUser,
  targetUser,
  onMessageSent,
  disabled = false
}) => {
  const [inputValue, setInputValue] = useState('');
  const [sending, setSending] = useState(false);
  const [recording, setRecording] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // 发送文本消息
  const sendTextMessage = async () => {
    if (!inputValue.trim() || sending) return;

    const messageData: CustomMessageData = {
      type: 2001,
      text: inputValue.trim()
    };

    await sendMessage(messageData);
    setInputValue('');
  };

  // 发送消息的通用方法
  const sendMessage = async (data: CustomMessageData) => {
    if (sending) return;

    setSending(true);
    try {
      const customMessage: CustomMessage = {
        fromUser: currentUser,
        toUser: targetUser,
        msgType: 2000, // 私聊消息
        time: Date.now().toString(),
        data
      };

      // 调用API发送消息
      const response = await postMessageSend(customMessage);
      
      if (response.success) {
        message.success('消息发送成功');
        onMessageSent?.(customMessage);
      } else {
        message.error('消息发送失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('消息发送失败');
    } finally {
      setSending(false);
    }
  };

  // 处理图片上传
  const handleImageUpload = (file: File) => {
    // 这里应该实现图片上传到服务器的逻辑
    // 暂时使用本地URL作为示例
    const imageUrl = URL.createObjectURL(file);
    
    const messageData: CustomMessageData = {
      type: 2002,
      image: imageUrl
    };

    sendMessage(messageData);
    return false; // 阻止默认上传行为
  };

  // 处理视频上传
  const handleVideoUpload = (file: File) => {
    // 这里应该实现视频上传到服务器的逻辑
    // 暂时使用本地URL作为示例
    const videoUrl = URL.createObjectURL(file);
    
    const messageData: CustomMessageData = {
      type: 2003,
      video: videoUrl
    };

    sendMessage(messageData);
    return false; // 阻止默认上传行为
  };

  // 开始录音
  const startRecording = () => {
    setRecording(true);
    // 这里应该实现录音功能
    message.info('录音功能开发中...');
    
    // 模拟录音结束
    setTimeout(() => {
      setRecording(false);
      // 模拟发送语音消息
      const messageData: CustomMessageData = {
        type: 2004,
        voice: 'mock_voice_url',
        voiceSeconds: 5
      };
      sendMessage(messageData);
    }, 2000);
  };

  // 停止录音
  const stopRecording = () => {
    setRecording(false);
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendTextMessage();
    }
  };

  // 更多功能菜单
  const moreActions = (
    <div className="more-actions">
      <Upload
        accept="image/*"
        showUploadList={false}
        beforeUpload={handleImageUpload}
        disabled={disabled || sending}
      >
        <Button 
          type="text" 
          icon={<PictureOutlined />}
          className="action-button"
        >
          图片
        </Button>
      </Upload>
      
      <Upload
        accept="video/*"
        showUploadList={false}
        beforeUpload={handleVideoUpload}
        disabled={disabled || sending}
      >
        <Button 
          type="text" 
          icon={<VideoCameraOutlined />}
          className="action-button"
        >
          视频
        </Button>
      </Upload>
      
      <Button 
        type="text" 
        icon={<AudioOutlined />}
        className="action-button"
        onClick={recording ? stopRecording : startRecording}
        disabled={disabled || sending}
      >
        {recording ? '停止录音' : '语音'}
      </Button>
    </div>
  );

  return (
    <div className="custom-message-input">
      <div className="input-toolbar">
        <Button 
          type="text" 
          icon={<SmileOutlined />}
          className="toolbar-button"
          disabled={disabled}
        />
        
        <Popover 
          content={moreActions} 
          trigger="click" 
          placement="topLeft"
        >
          <Button 
            type="text" 
            icon={<PlusOutlined />}
            className="toolbar-button"
            disabled={disabled}
          />
        </Popover>
      </div>
      
      <div className="input-area">
        <TextArea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="输入消息..."
          autoSize={{ minRows: 1, maxRows: 4 }}
          disabled={disabled}
          className="message-textarea"
        />
        
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={sendTextMessage}
          loading={sending}
          disabled={disabled || !inputValue.trim()}
          className="send-button"
        >
          发送
        </Button>
      </div>
      
      {recording && (
        <div className="recording-indicator">
          <div className="recording-animation"></div>
          <span>正在录音...</span>
        </div>
      )}
    </div>
  );
};

export default CustomMessageInput;
