import React, { useState, useEffect, useRef } from 'react';
import { Spin, Empty, message } from 'antd';
import CustomMessageComponent from '../CustomMessage';
import CustomMessageInput from '../CustomMessageInput';
import { CustomMessage, CustomUser } from '@/apis/exact';
import './index.less';

interface CustomChatProps {
  currentUser: CustomUser;
  targetUser?: CustomUser;
  messages?: CustomMessage[];
  loading?: boolean;
  onMessageSent?: (message: CustomMessage) => void;
  onLoadMoreMessages?: () => void;
}

const CustomChat: React.FC<CustomChatProps> = ({
  currentUser,
  targetUser,
  messages = [],
  loading = false,
  onMessageSent,
  onLoadMoreMessages
}) => {
  const [localMessages, setLocalMessages] = useState<CustomMessage[]>(messages);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // 同步外部消息
  useEffect(() => {
    setLocalMessages(messages);
  }, [messages]);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [localMessages]);

  // 处理消息发送
  const handleMessageSent = (message: CustomMessage) => {
    setLocalMessages(prev => [...prev, message]);
    onMessageSent?.(message);
  };

  // 处理图片点击
  const handleImageClick = (url: string) => {
    // 可以实现图片预览功能
    console.log('点击图片:', url);
  };

  // 处理视频点击
  const handleVideoClick = (url: string) => {
    // 可以实现视频播放功能
    console.log('点击视频:', url);
    message.info('视频播放功能开发中...');
  };

  // 处理语音点击
  const handleVoiceClick = (url: string, duration: number) => {
    // 可以实现语音播放功能
    console.log('点击语音:', url, duration);
    message.info('语音播放功能开发中...');
  };

  // 处理滚动加载更多
  const handleScroll = () => {
    if (!messagesContainerRef.current) return;
    
    const { scrollTop } = messagesContainerRef.current;
    if (scrollTop === 0 && onLoadMoreMessages) {
      onLoadMoreMessages();
    }
  };

  // 判断消息是否是自己发送的
  const isOwnMessage = (message: CustomMessage) => {
    return message.fromUser?.userId === currentUser.userId;
  };

  if (!targetUser) {
    return (
      <div className="custom-chat no-conversation">
        <Empty 
          description="请选择一个会话开始聊天"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div className="custom-chat">
      <div className="chat-header">
        <div className="target-user-info">
          <img 
            src={targetUser.avatar || '/default-avatar.png'} 
            alt={targetUser.nickname}
            className="target-avatar"
          />
          <div className="target-details">
            <h3 className="target-name">{targetUser.nickname}</h3>
            <span className="target-status">在线</span>
          </div>
        </div>
      </div>

      <div 
        className="chat-messages"
        ref={messagesContainerRef}
        onScroll={handleScroll}
      >
        {loading && (
          <div className="loading-more">
            <Spin size="small" />
            <span>加载更多消息...</span>
          </div>
        )}

        {localMessages.length === 0 && !loading ? (
          <div className="no-messages">
            <Empty 
              description="暂无消息，开始聊天吧"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          localMessages.map((message, index) => (
            <CustomMessageComponent
              key={`${message.time}-${index}`}
              message={message}
              isOwn={isOwnMessage(message)}
              onImageClick={handleImageClick}
              onVideoClick={handleVideoClick}
              onVoiceClick={handleVoiceClick}
            />
          ))
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <CustomMessageInput
        currentUser={currentUser}
        targetUser={targetUser}
        onMessageSent={handleMessageSent}
        disabled={loading}
      />
    </div>
  );
};

export default CustomChat;
