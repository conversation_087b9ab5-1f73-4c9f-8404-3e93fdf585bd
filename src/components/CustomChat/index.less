@import '@/styles/var';

.custom-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: @page-background;
  
  &.no-conversation {
    justify-content: center;
    align-items: center;
  }
  
  .chat-header {
    background-color: @white;
    border-bottom: 1px solid @line-1;
    padding: 12px 16px;
    flex-shrink: 0;
    
    .target-user-info {
      display: flex;
      align-items: center;
      
      .target-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 12px;
      }
      
      .target-details {
        .target-name {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: @text-color-5;
          line-height: 1.2;
        }
        
        .target-status {
          font-size: 12px;
          color: @success;
        }
      }
    }
  }
  
  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
    background-color: @page-background;
    
    .loading-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      color: @text-color-3;
      
      .ant-spin {
        margin-right: 8px;
      }
      
      span {
        font-size: 14px;
      }
    }
    
    .no-messages {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      min-height: 200px;
    }
    
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: @line-2;
      border-radius: 3px;
      
      &:hover {
        background-color: @line-3;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-chat {
    .chat-header {
      padding: 8px 12px;
      
      .target-user-info {
        .target-avatar {
          width: 32px;
          height: 32px;
          margin-right: 8px;
        }
        
        .target-details {
          .target-name {
            font-size: 14px;
          }
          
          .target-status {
            font-size: 11px;
          }
        }
      }
    }
    
    .chat-messages {
      padding: 8px 0;
    }
  }
}
