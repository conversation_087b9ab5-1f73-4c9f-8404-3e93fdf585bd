import { Ava<PERSON>, Flex } from "antd";
import React from "react";

import "./index.less";
import { getAvatarUrl } from "@/utils/common";

interface IUserInfo {
  children?: React.ReactNode;
  className?: string;
  name?: string;
  avatar?: string;
  role?: string;
}

const UserInfo: React.FC<IUserInfo> = ({ children, name, avatar, role }) => {
  console.log("getAvatarUrl(role)_avatar", avatar, getAvatarUrl(role));
  return (
    <Flex justify="space-between" align="center" className="common-user-info">
      <Flex align="center" gap={8} flex={1}>
        <Avatar
          className="common-user-info-avatar"
          src={avatar || getAvatarUrl(role)}
        />
        <span className="common-user-info-name segmented-text-ellipsis">
          {name}
        </span>
      </Flex>
      {children}
    </Flex>
  );
};

export default UserInfo;
