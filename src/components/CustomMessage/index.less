@import '@/styles/var';

.custom-message {
  display: flex;
  margin-bottom: 16px;
  padding: 0 16px;
  
  &.system {
    justify-content: center;
    
    .system-message {
      background-color: @line-1;
      color: @text-color-3;
      padding: 6px 12px;
      border-radius: 12px;
      font-size: 12px;
      
      .system-text {
        text-align: center;
      }
    }
  }
  
  &.other {
    flex-direction: row;
    
    .message-content {
      margin-left: 8px;
      max-width: 70%;
    }
    
    .message-bubble {
      background-color: @white;
      border: 1px solid @line-1;
      border-radius: 0 12px 12px 12px;
      padding: 8px 12px;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 8px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-right: 6px solid @line-1;
      }
      
      &::after {
        content: '';
        position: absolute;
        left: -5px;
        top: 8px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-right: 6px solid @white;
      }
    }
  }
  
  &.own {
    flex-direction: row-reverse;
    
    .message-content {
      margin-right: 8px;
      max-width: 70%;
      text-align: right;
    }
    
    .message-bubble {
      background-color: @primary-color;
      color: @white;
      border-radius: 12px 0 12px 12px;
      padding: 8px 12px;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        right: -6px;
        top: 8px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 6px solid @primary-color;
      }
    }
    
    .own-time {
      margin-top: 4px;
      font-size: 12px;
      color: @text-color-3;
    }
  }
  
  .message-avatar {
    flex-shrink: 0;
  }
  
  .message-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    
    .sender-name {
      font-size: 12px;
      color: @text-color-3;
      margin-right: 8px;
    }
    
    .message-time {
      font-size: 12px;
      color: @text-color-4;
    }
  }
  
  // 消息内容样式
  .text-message {
    word-wrap: break-word;
    line-height: 1.4;
  }
  
  .image-message {
    .ant-image {
      border-radius: 8px;
      overflow: hidden;
    }
  }
  
  .video-message {
    .video-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 200px;
      height: 120px;
      background-color: @line-1;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: @line-2;
      }
      
      .play-icon {
        font-size: 32px;
        color: @primary-color;
        margin-bottom: 8px;
      }
      
      span {
        font-size: 14px;
        color: @text-color-3;
      }
    }
  }
  
  .voice-message {
    .voice-button {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      border-radius: 20px;
      background-color: @line-1;
      border: none;
      color: @text-color-5;
      
      &:hover {
        background-color: @line-2;
      }
      
      .anticon {
        margin-right: 4px;
      }
    }
  }
  
  .unknown-message {
    color: @text-color-3;
    font-style: italic;
  }
}

// 在own消息中，调整颜色
.custom-message.own {
  .video-message .video-placeholder {
    background-color: rgba(255, 255, 255, 0.2);
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
    
    .play-icon {
      color: @white;
    }
    
    span {
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .voice-message .voice-button {
    background-color: rgba(255, 255, 255, 0.2);
    color: @white;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
}
