import React from 'react';
import { Avatar, Image, Button } from 'antd';
import { PlayCircleOutlined, SoundOutlined } from '@ant-design/icons';
import { CustomMessage, CustomMessageData } from '@/apis/exact';
import './index.less';

interface CustomMessageProps {
  message: CustomMessage;
  isOwn?: boolean; // 是否是自己发送的消息
  onImageClick?: (url: string) => void;
  onVideoClick?: (url: string) => void;
  onVoiceClick?: (url: string, duration: number) => void;
}

const CustomMessageComponent: React.FC<CustomMessageProps> = ({
  message,
  isOwn = false,
  onImageClick,
  onVideoClick,
  onVoiceClick
}) => {
  const { fromUser, data, time } = message;
  
  if (!data || !fromUser) {
    return null;
  }

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(parseInt(timestamp));
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // 渲染消息内容
  const renderMessageContent = () => {
    switch (data.type) {
      case 1001: // 系统通知
        return (
          <div className="system-message">
            <span className="system-text">{data.text}</span>
          </div>
        );
        
      case 2001: // 文本消息
        return (
          <div className="text-message">
            {data.text}
          </div>
        );
        
      case 2002: // 图片消息
        return (
          <div className="image-message">
            <Image
              src={data.image}
              alt="图片消息"
              style={{ maxWidth: 200, maxHeight: 200 }}
              onClick={() => onImageClick?.(data.image!)}
              preview={{
                src: data.image
              }}
            />
          </div>
        );
        
      case 2003: // 视频消息
        return (
          <div className="video-message">
            <div 
              className="video-placeholder"
              onClick={() => onVideoClick?.(data.video!)}
            >
              <PlayCircleOutlined className="play-icon" />
              <span>点击播放视频</span>
            </div>
          </div>
        );
        
      case 2004: // 语音消息
        return (
          <div className="voice-message">
            <Button
              type="text"
              icon={<SoundOutlined />}
              onClick={() => onVoiceClick?.(data.voice!, data.voiceSeconds || 0)}
              className="voice-button"
            >
              {data.voiceSeconds}"
            </Button>
          </div>
        );
        
      default:
        return (
          <div className="unknown-message">
            <span>不支持的消息类型</span>
          </div>
        );
    }
  };

  // 系统消息特殊处理
  if (data.type === 1001) {
    return (
      <div className="custom-message system">
        {renderMessageContent()}
      </div>
    );
  }

  return (
    <div className={`custom-message ${isOwn ? 'own' : 'other'}`}>
      {!isOwn && (
        <div className="message-avatar">
          <Avatar 
            src={fromUser.avatar} 
            size={40}
          >
            {fromUser.nickname?.charAt(0) || 'U'}
          </Avatar>
        </div>
      )}
      
      <div className="message-content">
        {!isOwn && (
          <div className="message-header">
            <span className="sender-name">{fromUser.nickname}</span>
            <span className="message-time">{formatTime(time || Date.now().toString())}</span>
          </div>
        )}
        
        <div className="message-bubble">
          {renderMessageContent()}
        </div>
        
        {isOwn && (
          <div className="message-time own-time">
            {formatTime(time || Date.now().toString())}
          </div>
        )}
      </div>
      
      {isOwn && (
        <div className="message-avatar">
          <Avatar 
            src={fromUser.avatar} 
            size={40}
          >
            {fromUser.nickname?.charAt(0) || 'U'}
          </Avatar>
        </div>
      )}
    </div>
  );
};

export default CustomMessageComponent;
