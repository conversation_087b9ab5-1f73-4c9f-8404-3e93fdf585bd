import React from 'react';
import { Avatar, Image, Button } from 'antd';
import { PlayCircleOutlined, SoundOutlined } from '@ant-design/icons';
import { CustomMessage, CustomMessageData } from '@/apis/exact';
import {
  parseMessageContent,
  getMessageText,
  getMessageImage,
  getMessageVideo,
  getMessageVoice,
  getMessageVoiceSeconds,
  printMessageDetails
} from '@/utils/messageParser';
import './index.less';

interface CustomMessageProps {
  message: CustomMessage;
  isOwn?: boolean; // 是否是自己发送的消息
  onImageClick?: (url: string) => void;
  onVideoClick?: (url: string) => void;
  onVoiceClick?: (url: string, duration: number) => void;
}

const CustomMessageComponent: React.FC<CustomMessageProps> = ({
  message,
  isOwn = false,
  onImageClick,
  onVideoClick,
  onVoiceClick
}) => {
  const { fromUser, data, time } = message;
  
  if (!data || !fromUser) {
    return null;
  }

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(parseInt(timestamp));
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // 渲染消息内容
  const renderMessageContent = () => {
    console.log('renderMessageContent - 消息数据:', data);
    console.log('renderMessageContent - 消息类型:', data.type);

    // 打印详细的消息信息用于调试
    printMessageDetails(data);

    switch (data.type) {
      case 1001: // 系统通知
        const systemText = getMessageText(data);
        console.log('renderMessageContent - 系统通知文本:', systemText);
        return (
          <div className="system-message">
            <span className="system-text">{systemText}</span>
          </div>
        );

      case 2001: // 文本消息
        const textContent = getMessageText(data);
        console.log('renderMessageContent - 文本消息内容:', textContent);
        return (
          <div className="text-message">
            {textContent}
          </div>
        );
        
      case 2002: // 图片消息
        const imageUrl = getMessageImage(data);
        console.log('renderMessageContent - 图片地址:', imageUrl);
        return (
          <div className="image-message">
            <Image
              src={imageUrl}
              alt="图片消息"
              style={{ maxWidth: 200, maxHeight: 200 }}
              onClick={() => onImageClick?.(imageUrl)}
              preview={{
                src: imageUrl
              }}
            />
          </div>
        );

      case 2003: // 视频消息
        const videoUrl = getMessageVideo(data);
        console.log('renderMessageContent - 视频地址:', videoUrl);
        return (
          <div className="video-message">
            <div
              className="video-placeholder"
              onClick={() => onVideoClick?.(videoUrl)}
            >
              <PlayCircleOutlined className="play-icon" />
              <span>点击播放视频</span>
            </div>
          </div>
        );

      case 2004: // 语音消息
        const voiceUrl = getMessageVoice(data);
        const voiceSeconds = getMessageVoiceSeconds(data);
        console.log('renderMessageContent - 语音地址:', voiceUrl, '时长:', voiceSeconds);
        return (
          <div className="voice-message">
            <Button
              type="text"
              icon={<SoundOutlined />}
              onClick={() => onVoiceClick?.(voiceUrl, voiceSeconds)}
              className="voice-button"
            >
              {voiceSeconds}"
            </Button>
          </div>
        );
        
      default:
        return (
          <div className="unknown-message">
            <span>不支持的消息类型</span>
          </div>
        );
    }
  };

  // 系统消息特殊处理
  if (data.type === 1001) {
    return (
      <div className="custom-message system">
        {renderMessageContent()}
      </div>
    );
  }

  return (
    <div className={`custom-message ${isOwn ? 'own' : 'other'}`}>
      {!isOwn && (
        <div className="message-avatar">
          <Avatar 
            src={fromUser.avatar} 
            size={40}
          >
            {fromUser.nickname?.charAt(0) || 'U'}
          </Avatar>
        </div>
      )}
      
      <div className="message-content">
        {!isOwn && (
          <div className="message-header">
            <span className="sender-name">{fromUser.nickname}</span>
            <span className="message-time">{formatTime(time || Date.now().toString())}</span>
          </div>
        )}
        
        <div className="message-bubble">
          {renderMessageContent()}
        </div>
        
        {isOwn && (
          <div className="message-time own-time">
            {formatTime(time || Date.now().toString())}
          </div>
        )}
      </div>
      
      {isOwn && (
        <div className="message-avatar">
          <Avatar 
            src={fromUser.avatar} 
            size={40}
          >
            {fromUser.nickname?.charAt(0) || 'U'}
          </Avatar>
        </div>
      )}
    </div>
  );
};

export default CustomMessageComponent;
