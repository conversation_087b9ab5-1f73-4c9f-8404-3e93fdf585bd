import TencentCloudChat, { Chat<PERSON><PERSON> } from '@tencentcloud/chat';
import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CacheUtil } from '@/utils/cache';
import dayjs from 'dayjs';
import { UserGetByTokenResponse } from '@/apis';
import { getAvatarUrl } from '@/utils/common';
import { CustomMessage, CustomUser } from '@/apis/exact';
import { Conversation } from '@/components/CustomConversationList';

let chat: ChatSDK;

export const initChat = (user: UserGetByTokenResponse) => {
  console.log('initChat user', user);
  const options = {
    SDKAppID: 1600063567,
  };
  // 创建 SDK 实例，`TencentCloudChat.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
  chat = TencentCloudChat.create(options); // SDK 实例通常用 chat 表示
  chat.setLogLevel(process.env.NODE_ENV === 'development' ? 0 : 1);
  
  if (user.userSig) {
    chat.login({
      userSig: user.userSig,
      userID: user.userId,
    });
    chat.on(TencentCloudChat.EVENT.SDK_READY, () => {
      updateMyProfile(user);
    });
  }
};

export const updateMyProfile = (user: UserGetByTokenResponse) => {
  chat.updateMyProfile({
    nick: user.nickname,
    avatar: user?.avatar || getAvatarUrl(user.roleId),
    role: Number(user.roleId),
  });
};

export const chatLoginOut = () => {
  return chat?.logout();
};

export const chatReadMessage = (conversationID: string) => {
  return chat?.setMessageRead({ conversationID });
};

const sortConversationList = (list: any[]) => {
  return list?.sort((a, b) => {
    const aCondition = a?.userProfile?.userID < 200000 ? 1 : 0;
    const bCondition = b?.userProfile?.userID < 200000 ? 1 : 0;
    return bCondition - aCondition; // 满足条件的排在前面
  });
};

// 将腾讯云IM会话转换为自定义会话格式
const convertIMConversationToCustom = (imConversation: any): Conversation => {
  const userProfile = imConversation.userProfile || {};
  const lastMessage = imConversation.lastMessage;
  
  return {
    id: imConversation.conversationID,
    user: {
      userId: userProfile.userID || '',
      nickname: userProfile.nick || userProfile.userID || '未知用户',
      avatar: userProfile.avatar || ''
    },
    lastMessage: lastMessage ? convertIMMessageToCustom(lastMessage) : undefined,
    unreadCount: imConversation.unreadCount || 0,
    lastMessageTime: lastMessage ? (lastMessage.time * 1000).toString() : Date.now().toString()
  };
};

// 将腾讯云IM消息转换为自定义消息格式
const convertIMMessageToCustom = (imMessage: any): CustomMessage => {
  const fromUser: CustomUser = {
    userId: imMessage.from || '',
    nickname: imMessage.nick || imMessage.from || '未知用户',
    avatar: imMessage.avatar || ''
  };
  
  const toUser: CustomUser = {
    userId: imMessage.to || '',
    nickname: '',
    avatar: ''
  };

  let messageData: any = { type: 2001 }; // 默认文本消息

  // 根据IM消息类型转换
  switch (imMessage.type) {
    case TencentCloudChat.TYPES.MSG_TEXT:
      messageData = {
        type: 2001,
        text: imMessage.payload?.text || ''
      };
      break;
    case TencentCloudChat.TYPES.MSG_IMAGE:
      messageData = {
        type: 2002,
        image: imMessage.payload?.imageInfoArray?.[0]?.url || ''
      };
      break;
    case TencentCloudChat.TYPES.MSG_VIDEO:
      messageData = {
        type: 2003,
        video: imMessage.payload?.videoUrl || ''
      };
      break;
    case TencentCloudChat.TYPES.MSG_SOUND:
      messageData = {
        type: 2004,
        voice: imMessage.payload?.url || '',
        voiceSeconds: imMessage.payload?.second || 0
      };
      break;
    case TencentCloudChat.TYPES.MSG_CUSTOM:
      try {
        const customData = JSON.parse(imMessage.payload?.data || '{}');
        messageData = customData;
      } catch (e) {
        messageData = {
          type: 2001,
          text: imMessage.payload?.description || '自定义消息'
        };
      }
      break;
    default:
      messageData = {
        type: 2001,
        text: '[不支持的消息类型]'
      };
  }

  return {
    fromUser,
    toUser,
    msgType: imMessage.conversationType === TencentCloudChat.TYPES.CONV_GROUP ? 1000 : 2000,
    time: (imMessage.time * 1000).toString(),
    data: messageData
  };
};

export const useConversation = () => {
  console.log('useConversation');
  const [conversation, setConversation] = useState<Conversation[]>(
    CacheUtil.get(CacheKey.CONVERSATION_LIST) || [],
  );
  
  const getChatConversation = async () => {
    try {
      if (!chat) return;
      const resp = await chat.getConversationList();
      console.log('conversation resp', resp);
      const imConversations = sortConversationList(resp?.data?.conversationList || []);
      const customConversations = imConversations.map(convertIMConversationToCustom);
      setConversation(customConversations);
      CacheUtil.set(CacheKey.CONVERSATION_LIST, customConversations);
    } catch (e) {
      console.log('getChatConversation error', e);
    }
  };
  
  useEffect(() => {
    if (chat?.on) {
      getChatConversation();
      chat.on(TencentCloudChat.EVENT.CONVERSATION_LIST_UPDATED, ({ data }) => {
        console.log('CONVERSATION_LIST_UPDATED', data);
        const imConversations = sortConversationList(data || []);
        const customConversations = imConversations.map(convertIMConversationToCustom);
        setConversation(customConversations);
        CacheUtil.set(CacheKey.CONVERSATION_LIST, customConversations);
      });
    }
  }, []);
  
  return conversation;
};

export const deleteConversation = (conversationID: string) => {
  return chat?.deleteConversation(conversationID);
};

export const useMessageList = ({ conversationID }: { conversationID?: string }) => {
  const [messageList, setMessageList] = useState<CustomMessage[]>([]);
  const [noMore, setNoMore] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const nextReqMessageIDRef = useRef<string | undefined>();
  
  const loadMessage = async () => {
    if (!conversationID || !chat) return;
    
    setLoading(true);
    try {
      const nextReqMessageID = nextReqMessageIDRef.current;
      const imResponse = await chat.getMessageList({
        conversationID,
        nextReqMessageID,
      });
      
      const imMessages = imResponse.data.messageList || [];
      const customMessages = imMessages.map(convertIMMessageToCustom);
      
      setMessageList(customMessages.reverse()); // 反转消息顺序，最新消息在底部
      
      if (imResponse.data.isCompleted) {
        setNoMore(true);
        nextReqMessageIDRef.current = undefined;
      } else {
        setNoMore(false);
        nextReqMessageIDRef.current = imResponse.data.nextReqMessageID;
      }
    } catch (e) {
      console.log('loadMessage error', e);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    if (conversationID) {
      nextReqMessageIDRef.current = undefined;
      setNoMore(false);
      loadMessage();
      
      // 监听新消息
      const handleNewMessage = ({ data }: { data: any[] }) => {
        console.log('MESSAGE_RECEIVED', data);
        const newCustomMessages = data
          .filter(msg => msg.conversationID === conversationID)
          .map(convertIMMessageToCustom);
        
        if (newCustomMessages.length > 0) {
          setMessageList(prev => [...prev, ...newCustomMessages]);
        }
      };
      
      chat?.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleNewMessage);
      
      return () => {
        chat?.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleNewMessage);
      };
    }
  }, [conversationID]);
  
  console.log('messageList', messageList);
  return { messageList, loadMessage, noMore, loading };
};

export const handleMessageTime = (
  timestamp: number | string,
  conversation?: boolean,
) => {
  const currentTime = dayjs();
  const messageTime = dayjs(Number(timestamp));

  const diffInMinutes = currentTime.diff(messageTime, 'minute');
  const diffInHours = currentTime.diff(messageTime, 'hour');
  const diffInDays = currentTime.diff(messageTime, 'day');

  if (diffInMinutes < 60 && conversation) {
    return diffInMinutes > 1 ? `${diffInMinutes}分钟前` : '1分钟前';
  } else if (diffInHours < 24) {
    return messageTime.format('HH:mm');
  } else if (diffInDays < 2) {
    return `昨天${messageTime.format('HH:mm')}`;
  } else if (diffInDays < 3) {
    return `前天${messageTime.format('HH:mm')}`;
  } else if (diffInDays < 7) {
    return `${messageTime.locale('zh-cn').format('dddd')}${
      conversation ? '' : messageTime.format('HH:mm')
    }`;
  } else if (currentTime.year() !== messageTime.year()) {
    return messageTime.format(
      conversation ? 'YYYY年M月D日' : 'YYYY年M月D日 HH:mm',
    );
  } else {
    return messageTime.format(conversation ? 'M月D日' : 'M月D日 HH:mm');
  }
};
