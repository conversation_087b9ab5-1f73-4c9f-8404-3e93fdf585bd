import React, { useState } from 'react';
import { Card, Button, Input, Select, Upload, message, Space, Divider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { CustomMessage, CustomUser, CustomMessageData } from '@/apis/exact';
import { postMessageSend } from '@/apis';
import { validateCustomMessage } from '@/utils/messageAdapter';
import CustomMessageComponent from '@/components/CustomMessage';
import './index.less';

const { TextArea } = Input;
const { Option } = Select;

const MessageDemo: React.FC = () => {
  const [messageType, setMessageType] = useState<number>(2001);
  const [textContent, setTextContent] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [videoUrl, setVideoUrl] = useState('');
  const [voiceUrl, setVoiceUrl] = useState('');
  const [voiceDuration, setVoiceDuration] = useState<number>(0);
  const [postId, setPostId] = useState('');
  const [sending, setSending] = useState(false);
  const [previewMessage, setPreviewMessage] = useState<CustomMessage | null>(null);

  // 模拟用户数据
  const fromUser: CustomUser = {
    userId: 'demo_user_001',
    nickname: '演示用户',
    avatar: 'https://via.placeholder.com/40x40'
  };

  const toUser: CustomUser = {
    userId: 'demo_user_002',
    nickname: '接收用户',
    avatar: 'https://via.placeholder.com/40x40'
  };

  // 构建消息数据
  const buildMessageData = (): CustomMessageData => {
    const data: CustomMessageData = { type: messageType };

    switch (messageType) {
      case 1001: // 系统通知
      case 2001: // 文本消息
        data.text = textContent;
        break;
      case 2002: // 图片消息
        data.image = imageUrl;
        break;
      case 2003: // 视频消息
        data.video = videoUrl;
        break;
      case 2004: // 语音消息
        data.voice = voiceUrl;
        data.voiceSeconds = voiceDuration;
        break;
    }

    if (postId) {
      data.postId = postId;
    }

    return data;
  };

  // 预览消息
  const handlePreview = () => {
    const messageData = buildMessageData();
    const customMessage: CustomMessage = {
      fromUser,
      toUser,
      msgType: messageType === 1001 ? 1000 : 2000,
      time: Date.now().toString(),
      data: messageData
    };

    if (!validateCustomMessage(customMessage)) {
      message.error('消息格式不完整，请检查必填字段');
      return;
    }

    setPreviewMessage(customMessage);
  };

  // 发送消息
  const handleSend = async () => {
    const messageData = buildMessageData();
    const customMessage: CustomMessage = {
      fromUser,
      toUser,
      msgType: messageType === 1001 ? 1000 : 2000,
      time: Date.now().toString(),
      data: messageData
    };

    if (!validateCustomMessage(customMessage)) {
      message.error('消息格式不完整，请检查必填字段');
      return;
    }

    setSending(true);
    try {
      const response = await postMessageSend(customMessage);
      if (response.success) {
        message.success('消息发送成功！');
        // 清空表单
        setTextContent('');
        setImageUrl('');
        setVideoUrl('');
        setVoiceUrl('');
        setVoiceDuration(0);
        setPostId('');
        setPreviewMessage(null);
      } else {
        message.error('消息发送失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('消息发送失败');
    } finally {
      setSending(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    setMessageType(2001);
    setTextContent('');
    setImageUrl('');
    setVideoUrl('');
    setVoiceUrl('');
    setVoiceDuration(0);
    setPostId('');
    setPreviewMessage(null);
  };

  return (
    <div className="message-demo">
      <Card title="自定义消息格式演示" className="demo-card">
        <div className="demo-content">
          <div className="form-section">
            <h3>消息配置</h3>
            
            <div className="form-item">
              <label>消息类型:</label>
              <Select
                value={messageType}
                onChange={setMessageType}
                style={{ width: 200 }}
              >
                <Option value={1001}>系统通知</Option>
                <Option value={2001}>文本消息</Option>
                <Option value={2002}>图片消息</Option>
                <Option value={2003}>视频消息</Option>
                <Option value={2004}>语音消息</Option>
              </Select>
            </div>

            {(messageType === 1001 || messageType === 2001) && (
              <div className="form-item">
                <label>文本内容:</label>
                <TextArea
                  value={textContent}
                  onChange={(e) => setTextContent(e.target.value)}
                  placeholder="请输入文本内容"
                  rows={3}
                />
              </div>
            )}

            {messageType === 2002 && (
              <div className="form-item">
                <label>图片地址:</label>
                <Input
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  placeholder="请输入图片URL"
                />
              </div>
            )}

            {messageType === 2003 && (
              <div className="form-item">
                <label>视频地址:</label>
                <Input
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                  placeholder="请输入视频URL"
                />
              </div>
            )}

            {messageType === 2004 && (
              <>
                <div className="form-item">
                  <label>语音地址:</label>
                  <Input
                    value={voiceUrl}
                    onChange={(e) => setVoiceUrl(e.target.value)}
                    placeholder="请输入语音URL"
                  />
                </div>
                <div className="form-item">
                  <label>语音时长(秒):</label>
                  <Input
                    type="number"
                    value={voiceDuration}
                    onChange={(e) => setVoiceDuration(Number(e.target.value))}
                    placeholder="请输入语音时长"
                  />
                </div>
              </>
            )}

            <div className="form-item">
              <label>帖子ID (可选):</label>
              <Input
                value={postId}
                onChange={(e) => setPostId(e.target.value)}
                placeholder="请输入关联的帖子ID"
              />
            </div>

            <Divider />

            <Space>
              <Button onClick={handlePreview}>预览消息</Button>
              <Button type="primary" onClick={handleSend} loading={sending}>
                发送消息
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </div>

          {previewMessage && (
            <div className="preview-section">
              <h3>消息预览</h3>
              <div className="message-preview">
                <CustomMessageComponent
                  message={previewMessage}
                  isOwn={true}
                />
              </div>
              
              <div className="json-preview">
                <h4>JSON格式:</h4>
                <pre>{JSON.stringify(previewMessage, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default MessageDemo;
