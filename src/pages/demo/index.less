@import '@/styles/var';

.message-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .demo-card {
    .ant-card-body {
      padding: 24px;
    }
  }
  
  .demo-content {
    display: flex;
    gap: 24px;
    
    .form-section {
      flex: 1;
      min-width: 400px;
      
      h3 {
        margin-bottom: 16px;
        color: @text-color-5;
        font-size: 16px;
        font-weight: 600;
      }
      
      .form-item {
        margin-bottom: 16px;
        
        label {
          display: block;
          margin-bottom: 4px;
          color: @text-color-5;
          font-weight: 500;
        }
        
        .ant-input,
        .ant-select,
        .ant-input-number {
          width: 100%;
        }
        
        .ant-select-selector {
          border-radius: 6px;
        }
        
        .ant-input {
          border-radius: 6px;
          
          &:focus {
            border-color: @primary-color;
            box-shadow: 0 0 0 2px rgba(48, 190, 143, 0.1);
          }
        }
      }
    }
    
    .preview-section {
      flex: 1;
      min-width: 400px;
      
      h3 {
        margin-bottom: 16px;
        color: @text-color-5;
        font-size: 16px;
        font-weight: 600;
      }
      
      h4 {
        margin: 16px 0 8px 0;
        color: @text-color-5;
        font-size: 14px;
        font-weight: 600;
      }
      
      .message-preview {
        background-color: @page-background;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        border: 1px solid @line-1;
      }
      
      .json-preview {
        pre {
          background-color: @line-1;
          border-radius: 6px;
          padding: 12px;
          font-size: 12px;
          color: @text-color-5;
          overflow-x: auto;
          white-space: pre-wrap;
          word-wrap: break-word;
          max-height: 300px;
          overflow-y: auto;
          
          // 自定义滚动条
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }
          
          &::-webkit-scrollbar-track {
            background: transparent;
          }
          
          &::-webkit-scrollbar-thumb {
            background-color: @line-2;
            border-radius: 3px;
            
            &:hover {
              background-color: @line-3;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .message-demo {
    padding: 12px;
    
    .demo-content {
      flex-direction: column;
      gap: 16px;
      
      .form-section,
      .preview-section {
        min-width: auto;
      }
    }
  }
}
