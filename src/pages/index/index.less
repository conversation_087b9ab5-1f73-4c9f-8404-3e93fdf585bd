@import '@/styles/var';

.chat-page {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: @page-background;

  &-left {
    flex: 1;
    display: flex;
    max-width: 70%;
    min-width: 60%;
    border-right: 1px solid @line-1;
  }

  &-right {
    width: 300px;
    min-width: 300px;
    padding: 16px;
    background-color: @white;
    overflow-y: auto;
  }

  &-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    gap: 16px;
    color: @text-color-4;
  }

  &-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: @error;
    font-size: 16px;
  }
}

.chat-disabled {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;

  &-message {
    text-align: center;
    color: @text-color-3;

    h3 {
      margin-bottom: 8px;
      color: @text-color-4;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.user-info-card {
  margin-bottom: 16px;

  .user-details {
    margin-top: 16px;

    .user-detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .label {
        color: @text-color-3;
        font-size: 14px;
      }

      .value {
        color: @text-color-5;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.order-info-card {
  .order-list {
    .order-item {
      padding: 12px;
      border: 1px solid @line-1;
      border-radius: 8px;
      margin-bottom: 12px;
      background-color: @white;

      &:last-child {
        margin-bottom: 0;
      }

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .order-no {
          font-size: 14px;
          color: @text-color-5;
          font-weight: 500;
        }

        .order-status {
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;

          &.status-0 {
            background-color: #fff7e6;
            color: #fa8c16;
          }

          &.status-100 {
            background-color: #f6ffed;
            color: @success;
          }

          &.status-200 {
            background-color: #fff1f0;
            color: @error;
          }
        }
      }

      .order-amount {
        font-size: 16px;
        color: @theme-color;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .order-time {
        font-size: 12px;
        color: @text-color-3;
      }
    }
  }

  .no-orders {
    text-align: center;
    color: @text-color-3;
    padding: 20px;
    font-size: 14px;
  }
}
