import "./index.less";

import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { TUILogin } from '@tencentcloud/tui-core';
import { ConversationList, Chat } from '@tencentcloud/chat-uikit-react';
import { UIKitProvider } from '@tencentcloud/uikit-base-component-react';
import '@tencentcloud/chat-uikit-react/dist/esm/index.css';
import { Card, Spin, message } from "antd";
import { postUserGetByToken, UserGetByTokenResponse, postOrderList, OrderListResponse } from "@/apis";
import UserInfo from "@/components/UserInfo";
import {CacheKey, CacheUtil} from "@/utils/cache";

const Index = () => {
  const [urlParams] = useSearchParams();
  const token = urlParams.get("token");

  // 调试信息
  console.log('=== Token Debug Info ===');
  console.log('Current URL:', window.location.href);
  console.log('URL Search Params:', window.location.search);
  console.log('Token from urlParams:', token);
  console.log('All URL params:', Object.fromEntries(urlParams.entries()));
  console.log('========================');

  const [userInfo, setUserInfo] = useState<UserGetByTokenResponse | null>(null);
  const [orderList, setOrderList] = useState<OrderListResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [imReady, setImReady] = useState(false);

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      if (!token) {
        console.warn("缺少token参数，使用模拟数据");
        // 返回模拟用户数据用于演示
        const mockUserData: UserGetByTokenResponse = {
          userId: "demo_user_001",
          phone: "138****8888",
          avatar: "",
          nickname: "演示用户",
          name: "张三",
          idCard: "110101199001011234",
          sex: 1,
          age: 30,
          birthday: "1990-01-01",
          mainBus: "农产品贸易",
          sign: "专业的农产品供应商",
          fans: "100",
          follows: "50",
          roleId: "1",
          token: "demo_token",
          userSig: "", // 空的userSig，IM功能将不可用
          certifyStatus: 1,
          addressInfo: {
            province: "北京市",
            city: 1,
            district: "朝阳区",
            street: "三里屯街道",
            community: "三里屯社区",
            address: "三里屯路1号",
            lng: "116.4074",
            lat: "39.9042"
          }
        };
        setUserInfo(mockUserData);
        return mockUserData;
      }

      const response = await postUserGetByToken();
      if (response.success && response.data) {
        setUserInfo(response.data);
        return response.data;
      } else {
        message.error("获取用户信息失败");
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
      message.error("获取用户信息失败");
    }
    return null;
  };

  // 获取订单列表
  const fetchOrderList = async () => {
    try {
      if (!token) {
        // 使用模拟订单数据
        const mockOrders: OrderListResponse[] = [
          {
            id: "order_001",
            orderNo: "ORD20241204001",
            tradeNo: "TRD20241204001",
            userId: "demo_user_001",
            nickname: "演示用户",
            roleId: 1,
            avatar: "",
            postId: "post_001",
            amount: 500.00,
            payType: "WX_APP",
            expireTime: "1733356800000",
            payTime: "1733270400000",
            createTime: "1733270400000",
            completeTime: "1733270500000",
            status: 100
          },
          {
            id: "order_002",
            orderNo: "ORD20241204002",
            tradeNo: "TRD20241204002",
            userId: "demo_user_001",
            nickname: "演示用户",
            roleId: 1,
            avatar: "",
            amount: 1200.00,
            payType: "UNION_APP",
            expireTime: "1733443200000",
            createTime: "1733356800000",
            status: 0
          }
        ];
        setOrderList(mockOrders);
        return;
      }

      const response = await postOrderList({ num: 1, row: 10 });
      if (response.success && response.data) {
        setOrderList(response.data);
      }
    } catch (error) {
      console.error("获取订单列表失败:", error);
    }
  };

  // 初始化IM
  const initIM = async (userData: UserGetByTokenResponse) => {
    try {
      // 检查是否有userSig，如果没有则跳过IM初始化
      if (!userData.userSig) {
        console.warn("缺少userSig，跳过IM初始化");
        setImReady(false);
        return;
      }

      const config = {
        SDKAppID: 1600063567, // 需要替换为实际的SDKAppID
        userID: userData.userId,
        userSig: userData.userSig,
      };

      await TUILogin.login({
        ...config,
        useUploadPlugin: true
      });

      setImReady(true);
    } catch (error) {
      console.error("IM初始化失败:", error);
      // 不显示错误消息，因为可能是配置问题
      setImReady(false);
    }
  };

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      const userData = await fetchUserInfo();
      console.log('userData', userData)
      if (userData) {
        await Promise.all([
          fetchOrderList(),
          initIM(userData)
        ]);
      }
      setLoading(false);
    };
    if (token) {
      CacheUtil.set(CacheKey.TOKEN, token);
      console.log('1233______', token)
      init();
    }
  }, [token]);

  if (loading) {
    return (
      <div className="chat-page-loading">
        <Spin size="large" />
        <div>正在初始化...</div>
      </div>
    );
  }

  if (!userInfo) {
    return (
      <div className="chat-page-error">
        <div>用户信息加载失败</div>
      </div>
    );
  }

  return (
    <div className="chat-page">
      <UIKitProvider language='zh-CN' theme='light'>
        <div className="chat-page-left">
          {imReady ? (
            <>
              <ConversationList />
              <Chat />
            </>
          ) : (
            <div className="chat-disabled">
              <div className="chat-disabled-message">
                <h3>聊天功能暂不可用</h3>
                <p>请联系管理员配置IM服务</p>
              </div>
            </div>
          )}
        </div>
        <div className="chat-page-right">
          <Card title="用户信息" className="user-info-card">
            <UserInfo
              name={userInfo.nickname}
              avatar={userInfo.avatar}
              role={userInfo.roleId}
            />
            <div className="user-details">
              <div className="user-detail-item">
                <span className="label">手机号:</span>
                <span className="value">{userInfo.phone}</span>
              </div>
              <div className="user-detail-item">
                <span className="label">主营业务:</span>
                <span className="value">{userInfo.mainBus}</span>
              </div>
              <div className="user-detail-item">
                <span className="label">个性签名:</span>
                <span className="value">{userInfo.sign}</span>
              </div>
            </div>
          </Card>

          <Card title="订单信息" className="order-info-card">
            {orderList.length > 0 ? (
              <div className="order-list">
                {orderList.map((order) => (
                  <div key={order.id} className="order-item">
                    <div className="order-header">
                      <span className="order-no">订单号: {order.orderNo}</span>
                      <span className={`order-status status-${order.status}`}>
                        {order.status === 0 ? '待支付' :
                         order.status === 100 ? '已支付' :
                         order.status === 200 ? '已退款' : '其他'}
                      </span>
                    </div>
                    <div className="order-amount">金额: ¥{order.amount}</div>
                    <div className="order-time">创建时间: {order.createTime}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-orders">暂无订单信息</div>
            )}
          </Card>
        </div>
      </UIKitProvider>
    </div>
  );
};

export default Index;
