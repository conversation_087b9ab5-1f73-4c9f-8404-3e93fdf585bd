import { 
  convertCustomToTIM, 
  convertTIMToCustom, 
  validateCustomMessage,
  TIMMessageType 
} from '../messageAdapter';
import { CustomMessage } from '@/apis/exact';

describe('MessageAdapter', () => {
  const mockCustomMessage: CustomMessage = {
    fromUser: {
      userId: 'user_001',
      nickname: '张三',
      avatar: 'https://example.com/avatar1.jpg'
    },
    toUser: {
      userId: 'user_002',
      nickname: '李四',
      avatar: 'https://example.com/avatar2.jpg'
    },
    msgType: 2000,
    time: '1640995200000',
    data: {
      type: 2001,
      text: '你好，这是一条测试消息'
    }
  };

  describe('convertCustomToTIM', () => {
    it('should convert text message correctly', () => {
      const timMessage = convertCustomToTIM(mockCustomMessage);
      
      expect(timMessage.from).toBe('user_001');
      expect(timMessage.to).toBe('user_002');
      expect(timMessage.type).toBe(TIMMessageType.TIM_MSG_TEXT);
      expect(timMessage.payload.text).toBe('你好，这是一条测试消息');
      expect(timMessage.conversationType).toBe('C2C');
    });

    it('should convert image message correctly', () => {
      const imageMessage: CustomMessage = {
        ...mockCustomMessage,
        data: {
          type: 2002,
          image: 'https://example.com/image.jpg'
        }
      };

      const timMessage = convertCustomToTIM(imageMessage);
      
      expect(timMessage.type).toBe(TIMMessageType.TIM_MSG_IMAGE);
      expect(timMessage.payload.imageInfoArray[0].url).toBe('https://example.com/image.jpg');
    });

    it('should convert system message to GROUP conversation', () => {
      const systemMessage: CustomMessage = {
        ...mockCustomMessage,
        msgType: 1000,
        data: {
          type: 1001,
          text: '系统通知'
        }
      };

      const timMessage = convertCustomToTIM(systemMessage);
      
      expect(timMessage.conversationType).toBe('GROUP');
    });

    it('should throw error for incomplete message', () => {
      const incompleteMessage: CustomMessage = {
        fromUser: mockCustomMessage.fromUser,
        // 缺少 toUser 和 data
      };

      expect(() => convertCustomToTIM(incompleteMessage)).toThrow('消息格式不完整');
    });
  });

  describe('validateCustomMessage', () => {
    it('should validate complete text message', () => {
      expect(validateCustomMessage(mockCustomMessage)).toBe(true);
    });

    it('should reject message without fromUser', () => {
      const invalidMessage: CustomMessage = {
        ...mockCustomMessage,
        fromUser: undefined
      };
      
      expect(validateCustomMessage(invalidMessage)).toBe(false);
    });

    it('should reject text message without text content', () => {
      const invalidMessage: CustomMessage = {
        ...mockCustomMessage,
        data: {
          type: 2001
          // 缺少 text 字段
        }
      };
      
      expect(validateCustomMessage(invalidMessage)).toBe(false);
    });

    it('should validate image message with image URL', () => {
      const imageMessage: CustomMessage = {
        ...mockCustomMessage,
        data: {
          type: 2002,
          image: 'https://example.com/image.jpg'
        }
      };
      
      expect(validateCustomMessage(imageMessage)).toBe(true);
    });

    it('should validate voice message with required fields', () => {
      const voiceMessage: CustomMessage = {
        ...mockCustomMessage,
        data: {
          type: 2004,
          voice: 'https://example.com/voice.mp3',
          voiceSeconds: 10
        }
      };
      
      expect(validateCustomMessage(voiceMessage)).toBe(true);
    });

    it('should reject voice message without duration', () => {
      const invalidVoiceMessage: CustomMessage = {
        ...mockCustomMessage,
        data: {
          type: 2004,
          voice: 'https://example.com/voice.mp3'
          // 缺少 voiceSeconds
        }
      };
      
      expect(validateCustomMessage(invalidVoiceMessage)).toBe(false);
    });
  });

  describe('convertTIMToCustom', () => {
    it('should convert TIM text message back to custom format', () => {
      const timMessage = convertCustomToTIM(mockCustomMessage);
      const convertedBack = convertTIMToCustom(timMessage as any);
      
      expect(convertedBack.data?.type).toBe(2001);
      expect(convertedBack.data?.text).toBe('你好，这是一条测试消息');
      expect(convertedBack.fromUser?.userId).toBe('user_001');
      expect(convertedBack.toUser?.userId).toBe('user_002');
    });
  });
});
