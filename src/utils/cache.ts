/**
 * 全局存储
 */

export enum CacheKey {
  TOKEN = "token",
  ROLE = "role",
  ORDER = "order",
  POST_ID = "postId",
  ORDER_TYPE = "orderType",
  CONVERSATION_LIST = "conversationList",
}

export class CacheUtil {
  static get(key: <PERSON><PERSON><PERSON><PERSON>) {
    const value = localStorage.getItem(key);
    return value && value !== "undefined" ? JSON.parse(value) : value;
  }

  static set(key: <PERSON><PERSON><PERSON><PERSON>, data: any) {
    if (data) {
      localStorage.setItem(key, JSON.stringify(data));
    }
  }

  static remove(key: <PERSON><PERSON><PERSON><PERSON>) {
    return localStorage.removeItem(key);
  }
}
