import { TradeListResponse as OrderListResponse } from "@/apis";

export enum OrderStatus {
  All = -1,
  Pending = 0,
  PendingSeller = 3,
  Completed = 100,
  Waiting = 1,
  WaitingCheck = 2,
  Cancel = -100,
  OutTime = -99,
  Refunding = 101,
  Refund = 200,
  // 自己加
  Hidden = 999,
}

export enum OrderType {
  Pork = 1, // 猪肉
  Pig = 2, // 生猪
  Assure = 3, // 担保交易
}
export enum OrderRole {
  Buyer = "buyer",
  Seller = "seller",
}

export const statusMapPork: Record<any, string> = {
  [OrderStatus.All]: "全部",
  [OrderStatus.Pending]: "待付款",
  [OrderStatus.Waiting]: "待验货",
  [OrderStatus.WaitingCheck]: "待收货",
  [OrderStatus.Completed]: "交易成功",
  [OrderStatus.Cancel]: "交易取消",
  [OrderStatus.OutTime]: "交易过期",
  [OrderStatus.Refunding]: "退款中",
  [OrderStatus.Refund]: "退款成功",
};

export const statusMap: Record<any, string> = {
  [OrderStatus.All]: "全部",
  [OrderStatus.Pending]: "买家待付保证金",
  [OrderStatus.PendingSeller]: "卖家待付保证金",
  [OrderStatus.Waiting]: "待验货",
  [OrderStatus.WaitingCheck]: "待收货",
  [OrderStatus.Completed]: "交易成功",
  [OrderStatus.Cancel]: "订单取消",
  [OrderStatus.OutTime]: "订单过期",
  [OrderStatus.Refunding]: "退单中",
  [OrderStatus.Refund]: "退单成功",
};

export const TabConfigs = [
  OrderStatus.All,
  OrderStatus.Pending,
  OrderStatus.Waiting,
  OrderStatus.WaitingCheck,
  OrderStatus.Completed,
  OrderStatus.Cancel,
  OrderStatus.Refunding,
  OrderStatus.Refund,
];
export const TabConfigsPig = [
  OrderStatus.All,
  OrderStatus.Pending,
  OrderStatus.PendingSeller,
  OrderStatus.Waiting,
  OrderStatus.WaitingCheck,
  OrderStatus.Completed,
  OrderStatus.Cancel,
  OrderStatus.Refunding,
  OrderStatus.Refund,
];
export const statusTipMap = {
  [OrderStatus.Waiting]: "出示取货码前，请先确保已验货无误，避免纠纷。",
  [OrderStatus.Refunding]: "卖家处理中，xx后未处理将自动退款",
};
export interface IOrder extends OrderListResponse {
  status: keyof typeof statusMap;
  refundStatus: RefundStatus;
}

export enum RefundStatus {
  request = 3,
  seller = 0,
  platform = 2,
  success = 100,
  fail = 1,
  back = 101,
}

export const StepsInfoName = {
  [RefundStatus.request]: "申请退款",
  [RefundStatus.seller]: "卖家处理",
  [RefundStatus.platform]: "平台介入",
  [RefundStatus.success]: "退款成功",
  [RefundStatus.fail]: "退款驳回",
  [RefundStatus.back]: "退款撤销",
};

export const getStepsInfo = (status: RefundStatus, isPlatform?: boolean) => {
  let back = [RefundStatus.request, RefundStatus.seller];
  if (isPlatform) {
    back.push(RefundStatus.platform);
  }
  switch (status) {
    case RefundStatus.seller:
      back.push(RefundStatus.success);
      break;
    case RefundStatus.platform:
      if (!isPlatform) {
        back.push(RefundStatus.platform);
      }
      back.push(RefundStatus.success);
      break;
    case RefundStatus.success:
      back.push(RefundStatus.success);
      break;
    case RefundStatus.fail:
      back.push(RefundStatus.fail);
      break;
  }
  return back.map((status) => ({ status, name: StepsInfoName[status] }));
};
