import { CustomMessageData, MessageContentData } from '@/apis/exact';

/**
 * 解析消息内容的工具函数
 */

// 解析消息的text字段中的JSON内容
export const parseMessageContent = (messageData: CustomMessageData): MessageContentData => {
  try {
    const content = JSON.parse(messageData.text);
    console.log('解析消息内容:', content);
    return JSON.parse(content.text);
  } catch (e) {
    console.warn('解析消息内容失败:', e, '原始text:', messageData.text);
    // 如果解析失败，尝试作为纯文本处理
    return {
      text: messageData.text
    };
  }
};

// 获取消息的文本内容
export const getMessageText = (messageData: CustomMessageData): string => {
  const content = parseMessageContent(messageData);
  return content.text || '';
};

// 获取消息的图片地址
export const getMessageImage = (messageData: CustomMessageData): string => {
  const content = parseMessageContent(messageData);
  return content.image || '';
};

// 获取消息的视频地址
export const getMessageVideo = (messageData: CustomMessageData): string => {
  const content = parseMessageContent(messageData);
  return content.video || '';
};

// 获取消息的语音地址
export const getMessageVoice = (messageData: CustomMessageData): string => {
  const content = parseMessageContent(messageData);
  return content.voice || '';
};

// 获取消息的语音时长
export const getMessageVoiceSeconds = (messageData: CustomMessageData): number => {
  const content = parseMessageContent(messageData);
  return content.voiceSeconds || 0;
};

// 获取消息的帖子ID
export const getMessagePostId = (messageData: CustomMessageData): string => {
  const content = parseMessageContent(messageData);
  return content.postId || '';
};

// 创建消息内容JSON字符串
export const createMessageText = (content: MessageContentData): string => {
  return JSON.stringify(content);
};

// 获取消息的显示文本（用于会话列表）
export const getMessageDisplayText = (messageData: CustomMessageData): string => {
  switch (messageData.type) {
    case 1001:
      return '[系统通知]';
    case 2001:
      return getMessageText(messageData) || '[文本消息]';
    case 2002:
      return '[图片]';
    case 2003:
      return '[视频]';
    case 2004:
      return '[语音]';
    default:
      return '[消息]';
  }
};

// 验证消息格式是否正确
export const validateMessageFormat = (messageData: CustomMessageData): boolean => {
  // 检查基本字段
  if (!messageData.type || typeof messageData.type !== 'number') {
    console.error('消息类型无效:', messageData.type);
    return false;
  }

  if (!messageData.text || typeof messageData.text !== 'string') {
    console.error('消息text字段无效:', messageData.text);
    return false;
  }

  // 尝试解析JSON
  try {
    const content = JSON.parse(messageData.text);

    // 根据消息类型验证必要字段
    switch (messageData.type) {
      case 1001: // 系统通知
      case 2001: // 文本消息
        if (!content.text) {
          console.error('文本消息缺少text字段');
          return false;
        }
        break;

      case 2002: // 图片消息
        if (!content.image) {
          console.error('图片消息缺少image字段');
          return false;
        }
        break;

      case 2003: // 视频消息
        if (!content.video) {
          console.error('视频消息缺少video字段');
          return false;
        }
        break;

      case 2004: // 语音消息
        if (!content.voice || !content.voiceSeconds) {
          console.error('语音消息缺少voice或voiceSeconds字段');
          return false;
        }
        break;

      default:
        console.error('未知的消息类型:', messageData.type);
        return false;
    }

    return true;
  } catch (e) {
    console.error('消息text字段不是有效的JSON:', e);
    return false;
  }
};

// 打印消息详情（用于调试）
export const printMessageDetails = (messageData: CustomMessageData) => {
  console.group('消息详情');
  console.log('消息类型:', messageData.type);
  console.log('原始text:', messageData.text);

  const content = parseMessageContent(messageData);
  console.log('解析后的内容:', content);

  console.group('内容字段');
  if (content.text) console.log('文本:', content.text);
  if (content.image) console.log('图片:', content.image);
  if (content.video) console.log('视频:', content.video);
  if (content.voice) console.log('语音:', content.voice);
  if (content.voiceSeconds) console.log('语音时长:', content.voiceSeconds, '秒');
  if (content.postId) console.log('帖子ID:', content.postId);
  console.groupEnd();

  const isValid = validateMessageFormat(messageData);
  console.log('格式验证:', isValid ? '✅ 通过' : '❌ 失败');

  console.groupEnd();
};
