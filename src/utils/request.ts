import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/utils/cache";
import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { decryptAES, encryptAES, generateSign } from "@/utils/safe";

import { BaseResponse } from "@/utils/interface";
import { showToast } from "@/utils/showToast";

export const DEFAULT_TIP_MESSAGE = "请求失败，请刷新重试";

const isDebug = import.meta.env.VITE_DEBUG === "1";

const log = (...args: any[]) => {
  if (import.meta.env.MODE !== "prod") {
    console.log(...args);
  }
};

const BASE_URL = import.meta.env.VITE_HOST;

console.log("import.meta.env", import.meta.env);

// 创建 Axios 实例
const instance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    const timestamp = Date.now().toString();
    const appId = "pc";
    const appSecret = "Cja2DjVxw8KT";
    const sign = generateSign(timestamp, appId, appSecret);

    config.headers["x-ca-app-id"] = appId;
    config.headers["x-ca-timestamp"] = timestamp;
    config.headers["x-ca-secret"] = sign;
    config.headers["device"] = "3";
    config.headers["appVersion"] = "1.0.0";
    config.headers["debug"] = isDebug ? "1" : "0";
    config.headers["token"] = CacheUtil.get(CacheKey.TOKEN) || "";

    if (config.data && !isDebug) {
      config.data = { body: encryptAES(JSON.stringify(config.data)) };
    }

    log("request", config.url, config);
    return config;
  },
  (error) => Promise.reject(error),
);

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse<BaseResponse<any>>) => {
    const { data } = response;

    if (!data.success && data?.msg) {
      console.log("data.msg", data.msg);
      setTimeout(() => {
        showToast(data.msg || DEFAULT_TIP_MESSAGE);
      }, 300);
      return data;
    }

    const result =
      data?.code === 0 && typeof data?.data === "string"
        ? {
            ...data,
            data: JSON.parse(decryptAES(data.data)),
            success: true,
          }
        : { ...(data || {}), success: true };

    log("response", response.config.url, result);
    return result;
  },
  (error) => {
    showToast(DEFAULT_TIP_MESSAGE);
    log("error", error);
    return Promise.reject(error);
  },
);

// 封装请求方法
const request = <T = any>(
  url: string,
  options: AxiosRequestConfig = {},
): Promise<BaseResponse<T>> => {
  const appUrl = `app/${url}`;
  return instance({
    url: import.meta.env.MODE === "dev" ? `/api/${appUrl}` : appUrl,
    ...options,
  });
};

export default request;
