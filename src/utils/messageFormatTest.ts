import { CustomMessage, CustomMessageData, MessageContentData } from '@/apis/exact';
import { createMessageText } from './messageParser';

/**
 * 测试消息格式解析的工具函数
 */

// 创建测试用的自定义消息
export const createTestCustomMessage = (type: number, content: any): CustomMessage => {
  let contentData: MessageContentData;
  let data: CustomMessageData;

  switch (type) {
    case 1001: // 系统通知
      contentData = {
        text: content.text || '系统通知消息'
      };
      data = {
        type: 1001,
        text: createMessageText(contentData)
      };
      break;

    case 2001: // 文本消息
      contentData = {
        text: content.text || '测试文本消息'
      };
      data = {
        type: 2001,
        text: createMessageText(contentData)
      };
      break;

    case 2002: // 图片消息
      contentData = {
        image: content.image || 'https://via.placeholder.com/200x200'
      };
      data = {
        type: 2002,
        text: createMessageText(contentData)
      };
      break;

    case 2003: // 视频消息
      contentData = {
        video: content.video || 'https://example.com/test.mp4'
      };
      data = {
        type: 2003,
        text: createMessageText(contentData)
      };
      break;

    case 2004: // 语音消息
      contentData = {
        voice: content.voice || 'https://example.com/test.mp3',
        voiceSeconds: content.voiceSeconds || 10
      };
      data = {
        type: 2004,
        text: createMessageText(contentData)
      };
      break;

    default:
      contentData = {
        text: '未知类型消息'
      };
      data = {
        type: 2001,
        text: createMessageText(contentData)
      };
  }

  // 添加postId如果提供
  if (content.postId) {
    try {
      const existingContent = JSON.parse(data.text);
      existingContent.postId = content.postId;
      data.text = createMessageText(existingContent);
    } catch (e) {
      console.warn('添加postId失败:', e);
    }
  }
  
  return {
    fromUser: {
      userId: 'test_user_001',
      nickname: '测试用户',
      avatar: 'https://via.placeholder.com/40x40'
    },
    toUser: {
      userId: 'test_user_002',
      nickname: '接收用户',
      avatar: 'https://via.placeholder.com/40x40'
    },
    msgType: type === 1001 ? 1000 : 2000,
    time: Date.now().toString(),
    data
  };
};

// 验证消息格式是否正确
export const validateMessageFormat = (message: CustomMessage): boolean => {
  console.log('验证消息格式:', message);
  
  // 检查基本结构
  if (!message.fromUser || !message.toUser || !message.data) {
    console.error('消息缺少基本字段');
    return false;
  }
  
  // 检查用户信息
  if (!message.fromUser.userId || !message.fromUser.nickname) {
    console.error('发送者信息不完整');
    return false;
  }
  
  if (!message.toUser.userId || !message.toUser.nickname) {
    console.error('接收者信息不完整');
    return false;
  }
  
  // 检查消息数据
  if (!message.data.type || typeof message.data.type !== 'number') {
    console.error('消息类型无效');
    return false;
  }
  
  // 检查text字段是否为有效JSON
  try {
    const content = JSON.parse(message.data.text);

    // 根据消息类型检查必要字段
    switch (message.data.type) {
      case 1001: // 系统通知
      case 2001: // 文本消息
        if (!content.text) {
          console.error('文本消息缺少text字段');
          return false;
        }
        break;

      case 2002: // 图片消息
        if (!content.image) {
          console.error('图片消息缺少image字段');
          return false;
        }
        break;

      case 2003: // 视频消息
        if (!content.video) {
          console.error('视频消息缺少video字段');
          return false;
        }
        break;

      case 2004: // 语音消息
        if (!content.voice || !content.voiceSeconds) {
          console.error('语音消息缺少voice或voiceSeconds字段');
          return false;
        }
        break;

      default:
        console.error('未知的消息类型:', message.data.type);
        return false;
    }
  } catch (e) {
    console.error('text字段不是有效的JSON:', e);
    return false;
  }
  
  console.log('消息格式验证通过');
  return true;
};

// 打印消息格式详情
export const printMessageDetails = (message: CustomMessage) => {
  console.group('消息详情');
  console.log('发送者:', message.fromUser);
  console.log('接收者:', message.toUser);
  console.log('消息类型:', message.msgType === 1000 ? '系统消息' : '私聊消息');
  console.log('时间戳:', message.time);
  console.log('消息数据:', message.data);
  
  console.group('数据字段详情');
  console.log('内容类型:', message.data.type);
  switch (message.data.type) {
    case 1001:
      console.log('系统通知文本:', message.data.text);
      break;
    case 2001:
      console.log('文本内容:', message.data.text);
      break;
    case 2002:
      console.log('图片地址:', message.data.image);
      break;
    case 2003:
      console.log('视频地址:', message.data.video);
      break;
    case 2004:
      console.log('语音地址:', message.data.voice);
      console.log('语音时长:', message.data.voiceSeconds, '秒');
      break;
  }
  if (message.data.postId) {
    console.log('关联帖子ID:', message.data.postId);
  }
  console.groupEnd();
  console.groupEnd();
};

// 测试所有消息类型
export const testAllMessageTypes = () => {
  console.group('测试所有消息类型');
  
  const testMessages = [
    createTestCustomMessage(1001, { text: '这是一条系统通知' }),
    createTestCustomMessage(2001, { text: '这是一条文本消息' }),
    createTestCustomMessage(2002, { image: 'https://example.com/image.jpg' }),
    createTestCustomMessage(2003, { video: 'https://example.com/video.mp4' }),
    createTestCustomMessage(2004, { voice: 'https://example.com/voice.mp3', voiceSeconds: 15 })
  ];
  
  testMessages.forEach((message, index) => {
    console.group(`测试消息 ${index + 1}`);
    printMessageDetails(message);
    const isValid = validateMessageFormat(message);
    console.log('验证结果:', isValid ? '✅ 通过' : '❌ 失败');
    console.groupEnd();
  });
  
  console.groupEnd();
};
