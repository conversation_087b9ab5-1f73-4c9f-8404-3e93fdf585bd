import { CustomMessage, CustomMessageData, CustomUser } from '@/apis/exact';

/**
 * 消息格式适配器
 * 用于在自定义消息格式和腾讯云IM消息格式之间进行转换
 */

// 腾讯云IM消息类型
export enum TIMMessageType {
  TIM_MSG_TEXT = 'TIMTextElem',
  TIM_MSG_IMAGE = 'TIMImageElem',
  TIM_MSG_VIDEO = 'TIMVideoFileElem',
  TIM_MSG_SOUND = 'TIMSoundElem',
  TIM_MSG_CUSTOM = 'TIMCustomElem',
}

// 腾讯云IM消息结构
export interface TIMMessage {
  ID?: string;
  type: string;
  payload: any;
  from: string;
  to: string;
  time: number;
  conversationID: string;
  conversationType: string;
  cloudCustomData?: string;
}

/**
 * 将自定义消息格式转换为腾讯云IM消息格式
 */
export function convertCustomToTIM(customMsg: CustomMessage): Partial<TIMMessage> {
  const { fromUser, toUser, msgType, time, data } = customMsg;
  
  if (!data || !fromUser || !toUser) {
    throw new Error('消息格式不完整');
  }

  const timMessage: Partial<TIMMessage> = {
    from: fromUser.userId,
    to: toUser.userId,
    time: time ? parseInt(time) : Date.now(),
    conversationType: msgType === 1000 ? 'GROUP' : 'C2C', // 系统消息用群聊，私聊消息用单聊
  };

  // 根据消息内容类型设置payload
  switch (data.type) {
    case 2001: // 文本消息
      timMessage.type = TIMMessageType.TIM_MSG_TEXT;
      timMessage.payload = {
        text: data.text || ''
      };
      break;
      
    case 2002: // 图片消息
      timMessage.type = TIMMessageType.TIM_MSG_IMAGE;
      timMessage.payload = {
        imageFormat: 'jpg',
        uuid: `img_${Date.now()}`,
        imageInfoArray: [{
          type: 1,
          size: 0,
          width: 0,
          height: 0,
          url: data.image || ''
        }]
      };
      break;
      
    case 2003: // 视频消息
      timMessage.type = TIMMessageType.TIM_MSG_VIDEO;
      timMessage.payload = {
        videoFormat: 'mp4',
        videoSecond: 0,
        videoSize: 0,
        videoUrl: data.video || '',
        videoUUID: `video_${Date.now()}`,
        thumbFormat: 'jpg',
        thumbWidth: 0,
        thumbHeight: 0,
        thumbSize: 0,
        thumbUrl: '',
        thumbUUID: `thumb_${Date.now()}`
      };
      break;
      
    case 2004: // 语音消息
      timMessage.type = TIMMessageType.TIM_MSG_SOUND;
      timMessage.payload = {
        url: data.voice || '',
        second: data.voiceSeconds || 0,
        size: 0,
        downloadFlag: 2
      };
      break;
      
    default: // 其他类型作为自定义消息处理
      timMessage.type = TIMMessageType.TIM_MSG_CUSTOM;
      timMessage.payload = {
        data: JSON.stringify(data),
        description: getMessageDescription(data),
        extension: JSON.stringify({
          fromUser,
          toUser,
          msgType,
          originalData: data
        })
      };
  }

  // 添加自定义数据到cloudCustomData
  timMessage.cloudCustomData = JSON.stringify({
    fromUser,
    toUser,
    msgType,
    originalTime: time,
    postId: data.postId
  });

  return timMessage;
}

/**
 * 将腾讯云IM消息格式转换为自定义消息格式
 */
export function convertTIMToCustom(timMsg: TIMMessage): CustomMessage {
  let customData: CustomMessageData = { type: 2001 }; // 默认文本消息
  
  // 解析cloudCustomData获取原始信息
  let originalInfo: any = {};
  if (timMsg.cloudCustomData) {
    try {
      originalInfo = JSON.parse(timMsg.cloudCustomData);
    } catch (e) {
      console.warn('解析cloudCustomData失败:', e);
    }
  }

  // 根据消息类型转换payload
  switch (timMsg.type) {
    case TIMMessageType.TIM_MSG_TEXT:
      customData = {
        type: 2001,
        text: timMsg.payload.text
      };
      break;
      
    case TIMMessageType.TIM_MSG_IMAGE:
      const imageInfo = timMsg.payload.imageInfoArray?.[0];
      customData = {
        type: 2002,
        image: imageInfo?.url || ''
      };
      break;
      
    case TIMMessageType.TIM_MSG_VIDEO:
      customData = {
        type: 2003,
        video: timMsg.payload.videoUrl
      };
      break;
      
    case TIMMessageType.TIM_MSG_SOUND:
      customData = {
        type: 2004,
        voice: timMsg.payload.url,
        voiceSeconds: timMsg.payload.second
      };
      break;
      
    case TIMMessageType.TIM_MSG_CUSTOM:
      try {
        const parsedData = JSON.parse(timMsg.payload.data);
        customData = parsedData;
      } catch (e) {
        customData = {
          type: 2001,
          text: timMsg.payload.description || '自定义消息'
        };
      }
      break;
  }

  // 构建自定义消息格式
  const customMessage: CustomMessage = {
    fromUser: originalInfo.fromUser || {
      userId: timMsg.from,
      nickname: timMsg.from,
      avatar: ''
    },
    toUser: originalInfo.toUser || {
      userId: timMsg.to,
      nickname: timMsg.to,
      avatar: ''
    },
    msgType: originalInfo.msgType || (timMsg.conversationType === 'GROUP' ? 1000 : 2000),
    time: (originalInfo.originalTime || timMsg.time).toString(),
    data: {
      ...customData,
      postId: originalInfo.postId
    }
  };

  return customMessage;
}

/**
 * 获取消息描述文本
 */
function getMessageDescription(data: CustomMessageData): string {
  switch (data.type) {
    case 1001:
      return '系统通知';
    case 2001:
      return data.text || '文本消息';
    case 2002:
      return '[图片]';
    case 2003:
      return '[视频]';
    case 2004:
      return '[语音]';
    default:
      return '消息';
  }
}

/**
 * 验证自定义消息格式
 */
export function validateCustomMessage(msg: CustomMessage): boolean {
  if (!msg.fromUser?.userId || !msg.toUser?.userId) {
    return false;
  }
  
  if (!msg.data?.type) {
    return false;
  }
  
  // 根据消息类型验证必要字段
  switch (msg.data.type) {
    case 2001: // 文本消息
      return !!msg.data.text;
    case 2002: // 图片消息
      return !!msg.data.image;
    case 2003: // 视频消息
      return !!msg.data.video;
    case 2004: // 语音消息
      return !!msg.data.voice && !!msg.data.voiceSeconds;
    default:
      return true;
  }
}
