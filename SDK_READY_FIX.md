# SDK 就绪状态修复说明

## 🔧 问题描述

之前的 `useConversation` 实现有问题：
1. 在 IM SDK 未就绪时就尝试获取会话列表
2. 没有等待 `TencentCloudChat.EVENT.SDK_READY` 事件
3. 可能导致获取会话失败或获取到空列表

## ✅ 修复方案

### 1. 重构 `initChat` 函数

```typescript
// 添加全局状态管理
let sdkReadyCallbacks: (() => void)[] = [];
let isSDKReady = false;

export const initChat = (user: UserGetByTokenResponse): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 创建和配置 SDK
    chat = TencentCloudChat.create(options);
    
    // 监听 SDK 就绪事件
    chat.on(TencentCloudChat.EVENT.SDK_READY, () => {
      console.log('IM SDK 已准备就绪');
      isSDKReady = true;
      
      // 触发所有等待的回调
      sdkReadyCallbacks.forEach(callback => callback());
      sdkReadyCallbacks = [];
      
      resolve();
    });
    
    // 监听错误事件
    chat.on(TencentCloudChat.EVENT.ERROR, (event) => {
      reject(event);
    });
  });
};
```

### 2. 添加 SDK 就绪回调机制

```typescript
// 添加SDK就绪回调
export const onSDKReady = (callback: () => void) => {
  if (isSDKReady) {
    callback(); // 如果已经就绪，立即执行
  } else {
    sdkReadyCallbacks.push(callback); // 否则加入等待队列
  }
};
```

### 3. 重构 `useConversation` 钩子

```typescript
export const useConversation = () => {
  const [conversation, setConversation] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const getChatConversation = async () => {
    if (!chat || !isSDKReady) {
      console.log('SDK 未就绪，跳过获取会话列表');
      return;
    }
    
    setIsLoading(true);
    try {
      const resp = await chat.getConversationList();
      // 处理会话列表...
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    // 当SDK就绪时，获取会话列表并设置监听
    onSDKReady(() => {
      console.log('SDK就绪，开始获取会话列表');
      getChatConversation();
      
      if (chat) {
        chat.on(TencentCloudChat.EVENT.CONVERSATION_LIST_UPDATED, handleUpdate);
      }
    });
    
    return () => {
      if (chat) {
        chat.off(TencentCloudChat.EVENT.CONVERSATION_LIST_UPDATED, handleUpdate);
      }
    };
  }, []);
  
  return { conversation, isLoading, refreshConversations: getChatConversation };
};
```

### 4. 更新主页面初始化

```typescript
useEffect(() => {
  const init = async () => {
    setLoading(true);
    const userData = await fetchUserInfo();
    if (userData) {
      await fetchOrderList();
      
      // 等待IM初始化完成
      try {
        await initChat(userData);
        console.log('IM 初始化成功');
      } catch (error) {
        console.error('IM 初始化失败:', error);
      }
    }
    setLoading(false);
  };
  
  if (token) {
    init();
  }
}, [token]);
```

### 5. 添加加载状态显示

```typescript
// 在会话列表组件中显示加载状态
{loading ? (
  <div className="loading-conversations">
    <Spin size="large" />
    <span>加载会话列表...</span>
  </div>
) : (
  // 会话列表内容
)}
```

## 🎯 修复效果

### 执行流程优化

1. **应用启动** → 获取用户信息
2. **初始化IM** → 等待 SDK_READY 事件
3. **SDK就绪** → 触发会话列表获取
4. **获取会话** → 显示真实的会话数据
5. **监听更新** → 实时同步会话变化

### 状态管理改进

- ✅ **SDK状态跟踪**：准确知道SDK是否就绪
- ✅ **回调队列**：确保在正确时机执行操作
- ✅ **加载状态**：用户体验更好的加载提示
- ✅ **错误处理**：完善的错误捕获和处理

### 调试信息增强

```javascript
// 控制台会显示详细的执行流程
console.log('IM SDK 已准备就绪');
console.log('SDK就绪，开始获取会话列表');
console.log('开始获取会话列表...');
console.log('转换后的会话列表:', customConversations);
```

## 🧪 测试验证

### 1. 启动应用
```bash
yarn dev
```

### 2. 观察控制台日志
应该看到以下顺序的日志：
1. `initChat user` - 开始初始化
2. `IM SDK 已准备就绪` - SDK就绪
3. `SDK就绪，开始获取会话列表` - 开始获取会话
4. `conversation resp` - 获取到会话数据
5. `转换后的会话列表` - 转换完成

### 3. 检查会话列表
- 应该显示真实的会话数据
- 不再是空列表或模拟数据
- 加载过程中显示加载动画

## 📋 关键改进点

1. **时序控制**：确保在SDK就绪后才获取数据
2. **状态管理**：清晰的加载和就绪状态
3. **错误处理**：完善的异常捕获机制
4. **用户体验**：加载状态的视觉反馈
5. **调试支持**：详细的日志输出

这个修复确保了会话列表能够在正确的时机获取真实的IM数据，解决了之前可能出现的空列表或获取失败的问题。
