# 新消息格式实现指南

## 🔄 格式变更说明

根据您的要求，我已经更新了消息格式。现在 `CustomMessageData` 的 `text` 字段是一个 JSON 字符串，包含所有消息内容。

### 新的消息格式

```typescript
export interface CustomMessageData {
  type: number; // 1001-系统通知 2001-文本消息 2002-图片消息 2003-视频消息 2004-语音消息
  text: string; // JSON字符串，包含具体的消息内容
}

// text字段中JSON的内容结构
export interface MessageContentData {
  text?: string;          // 文本内容
  image?: string;         // 图片地址
  video?: string;         // 视频地址
  voice?: string;         // 语音地址
  voiceSeconds?: number;  // 语音时长，秒
  postId?: string;        // 帖子ID
}
```

### 消息示例

#### 文本消息
```json
{
  "type": 2001,
  "text": "{\"text\":\"你好，这是一条文本消息\"}"
}
```

#### 图片消息
```json
{
  "type": 2002,
  "text": "{\"image\":\"https://example.com/image.jpg\"}"
}
```

#### 语音消息
```json
{
  "type": 2004,
  "text": "{\"voice\":\"https://example.com/voice.mp3\",\"voiceSeconds\":15}"
}
```

#### 带帖子ID的消息
```json
{
  "type": 2001,
  "text": "{\"text\":\"这是关于某个帖子的消息\",\"postId\":\"post_123\"}"
}
```

## 🛠️ 实现的更新

### 1. 消息解析工具 (`src/utils/messageParser.ts`)

创建了专门的工具函数来处理新格式：

```typescript
// 解析消息内容
export const parseMessageContent = (messageData: CustomMessageData): MessageContentData => {
  try {
    return JSON.parse(messageData.text);
  } catch (e) {
    return { text: messageData.text }; // 回退处理
  }
};

// 获取具体字段
export const getMessageText = (messageData: CustomMessageData): string;
export const getMessageImage = (messageData: CustomMessageData): string;
export const getMessageVideo = (messageData: CustomMessageData): string;
export const getMessageVoice = (messageData: CustomMessageData): string;
export const getMessageVoiceSeconds = (messageData: CustomMessageData): number;
export const getMessagePostId = (messageData: CustomMessageData): string;
```

### 2. 消息显示组件更新

所有显示组件都已更新以使用新的解析逻辑：

- **CustomMessage**: 使用解析工具获取具体内容
- **CustomConversationList**: 使用 `getMessageDisplayText` 显示摘要
- **CustomMessageInput**: 发送时创建正确的JSON格式

### 3. 消息转换逻辑更新

IM消息转换逻辑已更新以支持新格式：

```typescript
// 从IM消息转换为自定义格式
const convertIMMessageToCustom = (imMessage: any): CustomMessage => {
  // 创建内容对象
  let contentData: MessageContentData = {};
  
  // 根据IM消息类型填充内容
  switch (imMessage.type) {
    case TencentCloudChat.TYPES.MSG_TEXT:
      contentData = { text: imMessage.payload?.text || '' };
      break;
    // ... 其他类型
  }
  
  // 创建自定义消息格式
  return {
    // ...
    data: {
      type: 2001,
      text: JSON.stringify(contentData)
    }
  };
};
```

## 🧪 测试和验证

### 1. 启动应用
```bash
yarn dev
```

### 2. 测试步骤

1. **发送文本消息**
   - 在聊天界面输入文本并发送
   - 查看控制台日志，确认格式正确

2. **查看历史消息**
   - 选择有历史消息的会话
   - 检查消息是否正确解析和显示

3. **使用测试工具**
   - 点击"测试消息格式"按钮
   - 查看控制台输出的详细信息

### 3. 调试信息

我已经在关键位置添加了详细的调试日志：

```javascript
// 在控制台中查看
console.log('解析消息内容:', content);
console.log('消息详情:', messageDetails);
console.log('格式验证:', validationResult);
```

## 🔍 问题排查

### 常见问题

1. **消息显示为空白**
   - 检查 `text` 字段是否为有效JSON
   - 查看控制台错误信息

2. **历史消息格式不正确**
   - 旧消息可能使用不同格式
   - 转换逻辑会尝试兼容处理

3. **新消息发送失败**
   - 检查消息创建逻辑
   - 确认JSON格式正确

### 调试步骤

1. 打开浏览器控制台 (F12)
2. 发送一条测试消息
3. 查看详细的调试输出
4. 检查消息格式是否符合预期

## 📋 代码位置

相关文件已更新：

- `src/apis/exact.ts` - 消息类型定义
- `src/utils/messageParser.ts` - 消息解析工具
- `src/hooks/useChat.ts` - IM消息转换
- `src/components/CustomMessage/index.tsx` - 消息显示
- `src/components/CustomConversationList/index.tsx` - 会话列表
- `src/components/CustomMessageInput/index.tsx` - 消息输入
- `src/utils/messageFormatTest.ts` - 测试工具

## 🎯 验证清单

- [ ] 文本消息正确显示
- [ ] 图片消息正确显示
- [ ] 语音消息正确显示
- [ ] 会话列表显示正确的消息摘要
- [ ] 新发送的消息格式正确
- [ ] 历史消息能够正确解析
- [ ] 控制台没有格式错误
- [ ] 测试工具运行正常

请按照这个指南测试新的消息格式，如果遇到任何问题，请查看控制台日志并反馈具体的错误信息。
