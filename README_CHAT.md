# 聊天界面快速开始指南

## 🚀 快速开始

### 1. 安装依赖
```bash
yarn install
```

### 2. 启动开发服务器
```bash
yarn dev
```

### 3. 访问应用
打开浏览器访问：`http://localhost:5174`

## 📱 界面预览

### 布局结构
- **左侧**: 聊天界面（会话列表 + 聊天窗口）
- **右侧**: 信息面板（用户信息 + 订单信息）

### 功能特性
✅ 响应式布局  
✅ 用户信息展示  
✅ 订单列表展示  
✅ 聊天界面集成（需配置IM服务）  
✅ 演示模式（无需token）  
✅ 错误处理  

## 🔧 配置说明

### 环境变量
确保以下环境变量已配置：
- `VITE_HOST`: API服务器地址
- `VITE_DEBUG`: 调试模式（1=开启，0=关闭）

### IM服务配置
要启用聊天功能，需要：
1. 在腾讯云IM控制台创建应用
2. 获取SDKAppID
3. 配置userSig生成服务
4. 修改代码中的SDKAppID

## 🎯 使用场景

### 演示模式
直接访问 `http://localhost:5174` 即可查看界面效果，使用模拟数据。

### 生产模式
访问 `http://localhost:5174/?token=YOUR_TOKEN` 使用真实数据。

## 🛠️ 技术栈

- **前端框架**: React 18 + TypeScript
- **UI组件**: Ant Design + 腾讯云chat-uikit-react
- **路由**: React Router DOM
- **构建工具**: Vite
- **样式**: Less + PostCSS

## 📋 项目结构

```
src/
├── pages/index/          # 聊天主页面
│   ├── index.tsx        # 页面组件
│   └── index.less       # 页面样式
├── components/          # 公共组件
│   └── UserInfo/        # 用户信息组件
├── apis/               # API接口
├── utils/              # 工具函数
└── styles/             # 全局样式
```

## 🔍 故障排除

### 常见问题

**Q: 页面空白，控制台报错 "Cannot read properties of undefined (reading 'ReactCurrentDispatcher')"**  
A: 这是React版本兼容性问题，确保使用React 18.x版本。

**Q: 聊天功能显示"暂不可用"**  
A: 需要配置有效的SDKAppID和userSig。

**Q: 用户信息或订单信息不显示**  
A: 检查API接口是否正常，或使用演示模式查看效果。

### 开发调试
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的API请求状态

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本 >= 16.0.0
2. 依赖包版本是否正确
3. 环境变量配置
4. API服务是否正常

详细配置说明请参考 `CHAT_SETUP.md` 文件。
